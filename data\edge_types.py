"""
边类型常量定义
用于多模态对话图构建
"""

# 定义边类型常量 - 扩展的边类型分类
EDGE_TYPE_EMOTION_CAUSE = 0      # 情感-原因对边
EDGE_TYPE_TEMPORAL_FORWARD = 1   # 时序前向边 (i -> i+1, i+2, ...)
EDGE_TYPE_TEMPORAL_BACKWARD = 2  # 时序后向边 (i -> i-1, i-2, ...)
EDGE_TYPE_SAME_SPEAKER = 3       # 同说话者边
EDGE_TYPE_DIFF_SPEAKER = 4       # 不同说话者边

# 新增：情感语义边
EDGE_TYPE_SAME_EMOTION = 5       # 相同情感边
EDGE_TYPE_EMOTION_TRANSITION = 6 # 情感转换边
EDGE_TYPE_EMOTION_CONTRAST = 7   # 情感对比边

# 新增：语义相似性边
EDGE_TYPE_SEMANTIC_SIMILAR = 8   # 语义相似边

# 新增：因果依赖边
EDGE_TYPE_CAUSAL_DEPENDENCY = 9  # 因果依赖边

# 新增：对话结构边
EDGE_TYPE_QUESTION_ANSWER = 10   # 问答对边
EDGE_TYPE_RESPONSE_RELATION = 11 # 回应关系边
EDGE_TYPE_TOPIC_SHIFT = 12       # 话题转换边

# 边类型范围定义
EDGE_TYPE_RANGES = {
    'emotion_cause': (0, 0),
    'temporal_forward': (1, 1),
    'temporal_backward': (2, 2),
    'same_speaker': (3, 3),
    'diff_speaker': (4, 4)
}