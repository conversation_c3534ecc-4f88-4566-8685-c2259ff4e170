#!/usr/bin/env python3
"""
高级后处理器模块
专门针对F1分数提升的智能后处理策略
"""

import torch
import numpy as np
from typing import List, Tuple, Set, Dict, Optional, Union
from enum import Enum
import re
from collections import defaultdict, Counter


class ProcessingMode(Enum):
    """后处理模式"""
    BASIC = "basic"
    OPTIMAL = "optimal"
    ENHANCED = "enhanced"
    SMART_MATCHING = "smart_matching"


class UnifiedPostProcessor:
    """
    统一后处理器
    专门针对情感-原因对提取的智能后处理
    """

    def __init__(self, mode: str = "smart_matching",
                 token2idx: Optional[Dict] = None,
                 idx2token: Optional[Dict] = None,
                 confidence_threshold: float = 0.3):
        self.mode = ProcessingMode(mode)
        self.token2idx = token2idx or {}
        self.idx2token = idx2token or {}
        self.confidence_threshold = confidence_threshold

        # 有效情感集合
        self.valid_emotions = {
            'surprise', 'joy', 'sadness', 'neutral',
            'disgust', 'anger', 'fear', '_NONE'
        }

        # 情感相似度映射（用于智能匹配）
        self.emotion_similarity = {
            'joy': ['happiness', 'delight', 'pleasure'],
            'sadness': ['sorrow', 'grief', 'melancholy'],
            'anger': ['rage', 'fury', 'irritation'],
            'fear': ['anxiety', 'worry', 'terror'],
            'surprise': ['astonishment', 'amazement'],
            'disgust': ['revulsion', 'aversion'],
            'neutral': ['calm', 'normal', '_NONE']
        }

    def process(self, tokens: List[str], confidences: Optional[torch.Tensor] = None) -> List[Tuple[str, str, str]]:
        """
        处理token序列，提取情感-原因对

        Args:
            tokens: token序列
            confidences: 置信度分数（可选）

        Returns:
            pairs: 情感-原因对列表 [(emo_utt, emotion, cause_utt), ...]
        """
        if self.mode == ProcessingMode.BASIC:
            return self._basic_process(tokens)
        elif self.mode == ProcessingMode.OPTIMAL:
            return self._optimal_process(tokens)
        elif self.mode == ProcessingMode.ENHANCED:
            return self._enhanced_process(tokens, confidences)
        elif self.mode == ProcessingMode.SMART_MATCHING:
            return self._smart_matching_process(tokens, confidences)
        else:
            return self._basic_process(tokens)

    def _basic_process(self, tokens: List[str]) -> List[Tuple[str, str, str]]:
        """基础处理"""
        return self._extract_pairs_basic(tokens)

    def _optimal_process(self, tokens: List[str]) -> List[Tuple[str, str, str]]:
        """优化处理"""
        pairs = self._extract_pairs_basic(tokens)
        pairs = self._filter_invalid_pairs(pairs)
        pairs = self._remove_duplicates(pairs)
        return pairs

    def _enhanced_process(self, tokens: List[str], confidences: Optional[torch.Tensor] = None) -> List[Tuple[str, str, str]]:
        """增强处理"""
        pairs = self._extract_pairs_basic(tokens)
        pairs = self._filter_invalid_pairs(pairs)
        pairs = self._remove_duplicates(pairs)
        pairs = self._apply_confidence_filter(pairs, confidences)
        pairs = self._smart_emotion_correction(pairs)
        return pairs

    def _smart_matching_process(self, tokens: List[str], confidences: Optional[torch.Tensor] = None) -> List[Tuple[str, str, str]]:
        """智能匹配处理 - 专门针对F1提升"""
        # 1. 基础提取
        pairs = self._extract_pairs_basic(tokens)

        # 2. 智能话语ID修正
        pairs = self._smart_utterance_correction(pairs, tokens)

        # 3. 因果关系方向修正
        pairs = self._fix_causality_direction(pairs)

        # 4. 情感标签智能修正
        pairs = self._smart_emotion_correction(pairs)

        # 5. 去重和过滤
        pairs = self._advanced_deduplication(pairs)
        pairs = self._filter_invalid_pairs(pairs)

        # 6. 质量评分和排序
        pairs = self._rank_pairs_by_quality(pairs, tokens)

        return pairs

    def _extract_pairs_basic(self, tokens: List[str]) -> List[Tuple[str, str, str]]:
        """基础的对提取逻辑"""
        pairs = []

        if not tokens or len(tokens) < 4:
            return pairs

        # 移除特殊tokens
        if '<sos>' in tokens:
            start_idx = tokens.index('<sos>') + 1
        else:
            start_idx = 0

        if '<eos>' in tokens:
            end_idx = tokens.index('<eos>')
            tokens = tokens[start_idx:end_idx]
        else:
            tokens = tokens[start_idx:]

        # 按<sep>分割
        segments = []
        current_segment = []

        for token in tokens:
            if token == '<sep>':
                if len(current_segment) >= 3:
                    segments.append(current_segment)
                current_segment = []
            else:
                current_segment.append(token)

        # 添加最后一个段落
        if len(current_segment) >= 3:
            segments.append(current_segment)

        # 提取对
        for segment in segments:
            if len(segment) >= 3:
                emo_utt = segment[0]
                emotion = segment[1]
                cause_utt = segment[2]
                pairs.append((emo_utt, emotion, cause_utt))

        return pairs

    def _smart_utterance_correction(self, pairs: List[Tuple[str, str, str]], tokens: List[str]) -> List[Tuple[str, str, str]]:
        """智能话语ID修正"""
        corrected_pairs = []

        # 从tokens中提取所有出现的话语ID
        available_utts = set()
        for token in tokens:
            if token.startswith('utt_') and len(token) == 7:  # utt_XXX格式
                try:
                    utt_num = int(token[4:])
                    if 0 <= utt_num < 100:
                        available_utts.add(token)
                except ValueError:
                    continue

        for emo_utt, emotion, cause_utt in pairs:
            # 修正话语ID格式
            corrected_emo_utt = self._correct_utterance_id(emo_utt, available_utts)
            corrected_cause_utt = self._correct_utterance_id(cause_utt, available_utts)

            corrected_pairs.append((corrected_emo_utt, emotion, corrected_cause_utt))

        return corrected_pairs

    def _correct_utterance_id(self, utt_id: str, available_utts: Set[str]) -> str:
        """修正单个话语ID"""
        # 如果已经是有效格式且在可用集合中，直接返回
        if utt_id in available_utts:
            return utt_id

        # 尝试修正格式
        if utt_id.startswith('utt_'):
            try:
                utt_num = int(utt_id[4:])
                corrected = f'utt_{utt_num:03d}'
                if corrected in available_utts:
                    return corrected
            except ValueError:
                pass

        # 如果无法修正，返回最接近的可用话语ID
        if available_utts:
            # 简单策略：返回数字最小的可用话语ID
            return min(available_utts)

        # 如果没有可用话语ID，返回默认值
        return 'utt_000'

    def _fix_causality_direction(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """修正因果关系方向"""
        fixed_pairs = []

        for emo_utt, emotion, cause_utt in pairs:
            # 提取话语编号
            try:
                emo_num = int(emo_utt[4:]) if emo_utt.startswith('utt_') else 0
                cause_num = int(cause_utt[4:]) if cause_utt.startswith('utt_') else 0

                # 启发式规则：原因通常在情感之前或同时发生
                # 如果原因话语编号大于情感话语编号，可能需要交换
                if cause_num > emo_num + 2:  # 允许一定的时间差
                    # 交换位置
                    fixed_pairs.append((cause_utt, emotion, emo_utt))
                else:
                    fixed_pairs.append((emo_utt, emotion, cause_utt))

            except (ValueError, IndexError):
                # 如果无法解析，保持原样
                fixed_pairs.append((emo_utt, emotion, cause_utt))

        return fixed_pairs

    def _smart_emotion_correction(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """智能情感修正"""
        corrected_pairs = []

        for emo_utt, emotion, cause_utt in pairs:
            # 修正情感标签
            corrected_emotion = self._correct_emotion_label(emotion)
            corrected_pairs.append((emo_utt, corrected_emotion, cause_utt))

        return corrected_pairs

    def _correct_emotion_label(self, emotion: str) -> str:
        """修正情感标签"""
        # 如果已经是有效情感，直接返回
        if emotion in self.valid_emotions:
            return emotion

        # 尝试模糊匹配
        emotion_lower = emotion.lower()
        for valid_emotion in self.valid_emotions:
            if emotion_lower == valid_emotion.lower():
                return valid_emotion

        # 尝试相似度匹配
        for main_emotion, similar_emotions in self.emotion_similarity.items():
            if emotion_lower in [e.lower() for e in similar_emotions]:
                return main_emotion

        # 如果无法匹配，返回neutral
        return 'neutral'

    def _advanced_deduplication(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """高级去重策略"""
        if not pairs:
            return pairs

        # 1. 完全相同的对去重
        unique_pairs = list(set(pairs))

        # 2. 语义相似的对去重
        deduplicated = []
        for pair in unique_pairs:
            if not self._is_semantically_duplicate(pair, deduplicated):
                deduplicated.append(pair)

        return deduplicated

    def _is_semantically_duplicate(self, pair: Tuple[str, str, str], existing_pairs: List[Tuple[str, str, str]]) -> bool:
        """检查是否是语义重复的对"""
        emo_utt, emotion, cause_utt = pair

        for existing_emo_utt, existing_emotion, existing_cause_utt in existing_pairs:
            # 检查话语ID是否相同或相近
            if (self._are_utterances_similar(emo_utt, existing_emo_utt) and
                self._are_utterances_similar(cause_utt, existing_cause_utt) and
                self._are_emotions_similar(emotion, existing_emotion)):
                return True

        return False

    def _are_utterances_similar(self, utt1: str, utt2: str) -> bool:
        """检查两个话语ID是否相似"""
        if utt1 == utt2:
            return True

        # 提取数字部分比较
        try:
            num1 = int(utt1[4:]) if utt1.startswith('utt_') else -1
            num2 = int(utt2[4:]) if utt2.startswith('utt_') else -1

            # 如果数字相差很小，认为是相似的
            return abs(num1 - num2) <= 1
        except (ValueError, IndexError):
            return False

    def _are_emotions_similar(self, emotion1: str, emotion2: str) -> bool:
        """检查两个情感是否相似"""
        if emotion1 == emotion2:
            return True

        # 检查是否在同一个相似组中
        for main_emotion, similar_emotions in self.emotion_similarity.items():
            if emotion1 in similar_emotions and emotion2 in similar_emotions:
                return True
            if emotion1 == main_emotion and emotion2 in similar_emotions:
                return True
            if emotion2 == main_emotion and emotion1 in similar_emotions:
                return True

        return False

    def _rank_pairs_by_quality(self, pairs: List[Tuple[str, str, str]], tokens: List[str]) -> List[Tuple[str, str, str]]:
        """按质量对对进行排序"""
        if not pairs:
            return pairs

        # 计算每个对的质量分数
        scored_pairs = []
        for pair in pairs:
            score = self._calculate_pair_quality(pair, tokens)
            scored_pairs.append((score, pair))

        # 按分数降序排序
        scored_pairs.sort(key=lambda x: x[0], reverse=True)

        # 返回排序后的对
        return [pair for score, pair in scored_pairs]

    def _calculate_pair_quality(self, pair: Tuple[str, str, str], tokens: List[str]) -> float:
        """计算对的质量分数"""
        emo_utt, emotion, cause_utt = pair
        score = 0.0

        # 1. 话语ID有效性
        if self._is_valid_utterance_id(emo_utt):
            score += 2.0
        if self._is_valid_utterance_id(cause_utt):
            score += 2.0

        # 2. 情感有效性
        if emotion in self.valid_emotions:
            score += 3.0

        # 3. 情感多样性奖励
        if emotion not in ['neutral', '_NONE']:
            score += 1.0

        # 4. 因果关系合理性
        try:
            emo_num = int(emo_utt[4:]) if emo_utt.startswith('utt_') else 0
            cause_num = int(cause_utt[4:]) if cause_utt.startswith('utt_') else 0

            # 原因在情感之前或同时，更合理
            if cause_num <= emo_num:
                score += 1.5
            elif cause_num == emo_num + 1:
                score += 1.0
            else:
                score -= 0.5
        except (ValueError, IndexError):
            pass

        # 5. 在原始tokens中的出现频率
        if emo_utt in tokens:
            score += 0.5
        if cause_utt in tokens:
            score += 0.5
        if emotion in tokens:
            score += 0.5

        return score

    def _is_valid_utterance_id(self, utt_id: str) -> bool:
        """检查话语ID是否有效"""
        if not utt_id.startswith('utt_'):
            return False

        try:
            num = int(utt_id[4:])
            return 0 <= num < 100
        except (ValueError, IndexError):
            return False

    def _filter_invalid_pairs(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """过滤无效的对"""
        valid_pairs = []

        for emo_utt, emotion, cause_utt in pairs:
            # 检查基本有效性
            if (self._is_valid_utterance_id(emo_utt) and
                self._is_valid_utterance_id(cause_utt) and
                emotion in self.valid_emotions):
                valid_pairs.append((emo_utt, emotion, cause_utt))

        return valid_pairs

    def _remove_duplicates(self, pairs: List[Tuple[str, str, str]]) -> List[Tuple[str, str, str]]:
        """移除重复的对"""
        return list(set(pairs))

    def _apply_confidence_filter(self, pairs: List[Tuple[str, str, str]], confidences: Optional[torch.Tensor] = None) -> List[Tuple[str, str, str]]:
        """应用置信度过滤"""
        if confidences is None or len(pairs) == 0:
            return pairs

        # 简单的置信度过滤策略
        # 这里可以根据具体需求实现更复杂的逻辑
        return pairs


# 便利函数
def create_unified_processor(mode: str = "smart_matching", **kwargs) -> UnifiedPostProcessor:
    """创建统一后处理器"""
    return UnifiedPostProcessor(mode=mode, **kwargs)


def process_predictions_unified(predictions: List[str], mode: str = "smart_matching", **kwargs) -> List[Tuple[str, str, str]]:
    """统一处理预测结果"""
    processor = create_unified_processor(mode=mode, **kwargs)
    return processor.process(predictions)