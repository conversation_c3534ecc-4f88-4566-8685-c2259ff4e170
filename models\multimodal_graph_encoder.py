"""
多模态图编码器
直接使用增强多模态图构建策略，不再依赖已删除的融合模块
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional

from .graph_encoders import GNN, EnhancedGATEncoder
from .enhanced_multimodal_graph import EnhancedMultimodalGraphBuilder, MultimodalEdgeTypeEncoder


class MultimodalGraphEncoder(nn.Module):
    """
    多模态图编码器
    直接使用增强多模态图构建策略进行多模态图编码
    """
    
    def __init__(self,
                 text_dim: int = 768,
                 visual_dim: int = 4096,
                 audio_dim: int = 6373,
                 hidden_dim: int = 256,
                 num_layers: int = 2,
                 num_heads: int = 8,
                 num_edge_types: int = 5,
                 n_speakers: int = 10,
                 modalities: List[str] = ['text', 'visual', 'audio'],
                 encoder_type: str = 'gat',  # 'gnn' or 'gat'
                 use_speaker_embedding: bool = True,
                 use_modal_embedding: bool = True,
                 use_multimodal_graph: bool = True,
                 dropout: float = 0.2,
                 pooling_method: str = 'attention'):
        """
        初始化多模态图编码器
        
        Args:
            text_dim: 文本特征维度
            visual_dim: 视觉特征维度
            audio_dim: 音频特征维度
            hidden_dim: 隐藏层维度
            num_layers: 图卷积层数
            num_heads: 注意力头数（GAT）
            num_edge_types: 边类型数量
            n_speakers: 说话者数量
            modalities: 使用的模态列表
            encoder_type: 编码器类型
            use_speaker_embedding: 是否使用说话者嵌入
            use_modal_embedding: 是否使用模态嵌入
            use_multimodal_graph: 是否使用多模态图结构
            dropout: dropout率
            pooling_method: 池化方法
        """
        super().__init__()
        
        self.modalities = modalities
        self.hidden_dim = hidden_dim
        self.encoder_type = encoder_type
        self.use_multimodal_graph = use_multimodal_graph
        self.visual_dim = visual_dim
        self.audio_dim = audio_dim
        
        # 模态特征投影层
        if 'text' in modalities:
            self.text_fc = nn.Linear(text_dim, hidden_dim)
        if 'visual' in modalities:
            self.visual_fc = nn.Linear(visual_dim, hidden_dim)
        if 'audio' in modalities:
            self.audio_fc = nn.Linear(audio_dim, hidden_dim)
        
        # 说话者嵌入
        if use_speaker_embedding:
            self.speaker_embeddings = nn.Embedding(n_speakers, hidden_dim)
            if 'text' in modalities:
                self.text_spk_embs = nn.Embedding(n_speakers, hidden_dim)
            if 'visual' in modalities:
                self.visual_spk_embs = nn.Embedding(n_speakers, hidden_dim)
            if 'audio' in modalities:
                self.audio_spk_embs = nn.Embedding(n_speakers, hidden_dim)
        
        # 模态嵌入
        if use_modal_embedding:
            self.modal_embeddings = nn.Embedding(len(modalities), hidden_dim)
        
        # 图编码器
        if encoder_type == 'gat':
            self.graph_encoder = EnhancedGATEncoder(
                input_size=hidden_dim,
                hidden_size=hidden_dim,
                num_layers=num_layers,
                num_heads=num_heads,
                num_edge_types=num_edge_types,
                dropout=dropout,
                pooling_method=pooling_method
            )
        else:
            self.graph_encoder = GNN(
                in_channels=hidden_dim,
                hidden_channels=hidden_dim,
                out_channels=hidden_dim,
                num_layers=num_layers,
                dropout=dropout,
                num_edge_types=num_edge_types,
                pooling_method=pooling_method
            )
        
        # 增强多模态图构建器
        if use_multimodal_graph:
            self.enhanced_graph_builder = EnhancedMultimodalGraphBuilder(
                connection_strategy='enhanced_cosine',
                cross_modal_strategy='speaker_aware',
                window_size=10,
                similarity_threshold=0.1,
                normalize_adj=True
            )
            
            self.edge_type_encoder = MultimodalEdgeTypeEncoder(
                n_modals=len(modalities)
            )
        
        # 激活函数和dropout
        self.act_fn = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.1)
    
    def forward(self,
                text_features: torch.Tensor,
                visual_features: List[torch.Tensor],
                audio_features: List[torch.Tensor],
                speaker_ids: torch.Tensor,
                utterance_lengths: torch.Tensor,
                edge_index: torch.Tensor,
                edge_type: torch.Tensor,
                edge_norm: torch.Tensor,
                batch_indices: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            text_features: 文本特征 [batch_size, max_utts, text_dim]
            visual_features: 视觉特征列表
            audio_features: 音频特征列表
            speaker_ids: 说话者ID [batch_size, max_utts]
            utterance_lengths: 话语数量 [batch_size]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            edge_norm: 边权重 [num_edges]
            batch_indices: batch索引 [total_nodes]
            
        Returns:
            node_embeddings: 节点嵌入 [total_nodes, hidden_dim]
            graph_embedding: 图嵌入 [batch_size, hidden_dim]
        """
        device = text_features.device
        
        if self.use_multimodal_graph and len(self.modalities) > 1:
            # 使用增强多模态图结构
            return self._encode_multimodal_graph(
                text_features, visual_features, audio_features, 
                speaker_ids, utterance_lengths, batch_indices
            )
        else:
            # 使用原始图结构，只处理文本特征
            batch_size = text_features.size(0)
            fused_features = []
            
            for b in range(batch_size):
                cur_len = min(utterance_lengths[b].item(), text_features.size(1))
                if cur_len <= 0:
                    continue
                    
                # 处理文本特征
                text_feat = text_features[b, :cur_len]
                text_feat = self.text_fc(text_feat)
                
                # 添加说话者嵌入
                if hasattr(self, 'text_spk_embs'):
                    spk_ids = speaker_ids[b, :cur_len]
                    spk_ids = torch.clamp(spk_ids, 0, self.text_spk_embs.num_embeddings - 1)
                    text_spk_emb = self.text_spk_embs(spk_ids)
                    text_feat = text_feat + text_spk_emb
                
                fused_features.append(text_feat)
            
            # 合并所有特征
            if fused_features:
                all_features = torch.cat(fused_features, dim=0)
            else:
                all_features = torch.zeros((1, self.hidden_dim), device=device)
            
            return self.graph_encoder(all_features, edge_index, edge_type, edge_norm, batch_indices)
    
    def _encode_multimodal_graph(self,
                                text_features: torch.Tensor,
                                visual_features: List[torch.Tensor],
                                audio_features: List[torch.Tensor],
                                speaker_ids: torch.Tensor,
                                utterance_lengths: torch.Tensor,
                                batch_indices: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用增强多模态图结构进行编码
        """
        device = text_features.device
        batch_size = text_features.size(0)
        
        # 准备多模态特征列表
        features_list = []
        all_batch_indices = []
        
        # 处理每个batch
        for b in range(batch_size):
            cur_len = min(utterance_lengths[b].item(), text_features.size(1))
            if cur_len <= 0:
                continue
                
            modal_features = []
            
            # 处理文本特征
            if 'text' in self.modalities:
                text_feat = text_features[b, :cur_len]
                text_feat = self.text_fc(text_feat)
                
                # 添加说话者嵌入
                if hasattr(self, 'text_spk_embs'):
                    spk_ids = speaker_ids[b, :cur_len]
                    spk_ids = torch.clamp(spk_ids, 0, self.text_spk_embs.num_embeddings - 1)
                    text_spk_emb = self.text_spk_embs(spk_ids)
                    text_feat = text_feat + text_spk_emb
                
                modal_features.append(text_feat)
            
            # 处理视觉特征
            if 'visual' in self.modalities and b < len(visual_features):
                visual_feat = visual_features[b][:cur_len]
                visual_feat = self.visual_fc(visual_feat)
                
                # 添加说话者嵌入
                if hasattr(self, 'visual_spk_embs'):
                    spk_ids = speaker_ids[b, :cur_len]
                    spk_ids = torch.clamp(spk_ids, 0, self.visual_spk_embs.num_embeddings - 1)
                    visual_spk_emb = self.visual_spk_embs(spk_ids)
                    visual_feat = visual_feat + visual_spk_emb
                
                modal_features.append(visual_feat)
            
            # 处理音频特征
            if 'audio' in self.modalities and b < len(audio_features):
                audio_feat = audio_features[b][:cur_len]
                audio_feat = self.audio_fc(audio_feat)
                
                # 添加说话者嵌入
                if hasattr(self, 'audio_spk_embs'):
                    spk_ids = speaker_ids[b, :cur_len]
                    spk_ids = torch.clamp(spk_ids, 0, self.audio_spk_embs.num_embeddings - 1)
                    audio_spk_emb = self.audio_spk_embs(spk_ids)
                    audio_feat = audio_feat + audio_spk_emb
                
                modal_features.append(audio_feat)
            
            # 添加模态嵌入
            if hasattr(self, 'modal_embeddings'):
                modal_indices = torch.arange(len(modal_features), device=device)
                modal_embs = self.modal_embeddings(modal_indices)
                
                for i, feat in enumerate(modal_features):
                    modal_features[i] = feat + modal_embs[i].unsqueeze(0).expand_as(feat)
            
            # 收集特征
            for feat in modal_features:
                features_list.append(feat)
            
            # 创建batch索引
            for _ in range(len(modal_features)):
                all_batch_indices.append(torch.full((cur_len,), b, dtype=torch.long, device=device))
        
        # 合并所有特征
        if features_list:
            all_features = torch.cat(features_list, dim=0)
            final_batch_indices = torch.cat(all_batch_indices, dim=0)
        else:
            all_features = torch.zeros((1, self.hidden_dim), device=device)
            final_batch_indices = torch.zeros(1, dtype=torch.long, device=device)
        
        # 构建增强多模态图
        utterance_lengths_list = [utterance_lengths[b].item() for b in range(batch_size)]
        
        # 重新组织特征列表按模态分组
        modal_features_list = []
        total_length = sum(utterance_lengths_list)
        
        for modal_idx in range(len(self.modalities)):
            modal_feat_list = []
            start_idx = 0
            
            for b in range(batch_size):
                cur_len = utterance_lengths_list[b]
                if cur_len <= 0:
                    continue
                
                # 计算当前模态在all_features中的位置
                modal_start = start_idx + modal_idx * cur_len
                modal_end = modal_start + cur_len
                
                if modal_end <= all_features.size(0):
                    modal_feat_list.append(all_features[modal_start:modal_end])
                
                start_idx += len(self.modalities) * cur_len
            
            if modal_feat_list:
                modal_features_list.append(torch.cat(modal_feat_list, dim=0))
        
        # 如果特征列表为空，使用简单图编码
        if not modal_features_list:
            edge_index = torch.arange(all_features.size(0), device=device).unsqueeze(0).repeat(2, 1)
            edge_type = torch.zeros(all_features.size(0), dtype=torch.long, device=device)
            edge_norm = torch.ones(all_features.size(0), device=device)
            
            return self.graph_encoder(all_features, edge_index, edge_type, edge_norm, final_batch_indices)
        
        # 构建多模态邻接矩阵
        adj_matrix = self.enhanced_graph_builder.create_multimodal_adjacency(
            features_list=modal_features_list,
            utterance_lengths=utterance_lengths_list,
            speaker_ids=speaker_ids.flatten()[:total_length],
            modalities=self.modalities
        )
        
        # 从邻接矩阵创建边索引和边权重
        edge_index, edge_weights = self.enhanced_graph_builder.create_edge_index_from_adjacency(adj_matrix)
        
        # 编码边类型
        edge_types = self.edge_type_encoder.encode_edge_types(
            adj_matrix=adj_matrix,
            features_list=modal_features_list,
            utterance_lengths=utterance_lengths_list,
            speaker_ids=speaker_ids.flatten()[:total_length]
        )
        
        # 使用图编码器
        node_embeddings, graph_embedding = self.graph_encoder(
            all_features, edge_index, edge_types, edge_weights, final_batch_indices
        )
        
        return node_embeddings, graph_embedding


class MultimodalGCNLayer(nn.Module):
    """
    多模态图卷积层
    借鉴MMGCN的GCNII结构
    """
    
    def __init__(self, in_features: int, out_features: int, 
                 alpha: float = 0.1, lambda_param: float = 0.5,
                 variant: bool = False, residual: bool = False):
        super().__init__()
        
        self.variant = variant
        self.residual = residual
        self.alpha = alpha
        self.lambda_param = lambda_param
        
        if variant:
            self.in_features = 2 * in_features
        else:
            self.in_features = in_features
            
        self.out_features = out_features
        self.weight = nn.Parameter(torch.FloatTensor(self.in_features, out_features))
        
        self.reset_parameters()
    
    def reset_parameters(self):
        stdv = 1. / np.sqrt(self.out_features)
        self.weight.data.uniform_(-stdv, stdv)
    
    def forward(self, input_feat: torch.Tensor, adj: torch.Tensor, 
                h0: torch.Tensor, layer_idx: int) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input_feat: 输入特征
            adj: 邻接矩阵
            h0: 初始特征
            layer_idx: 层索引
            
        Returns:
            输出特征
        """
        theta = np.log(self.lambda_param / layer_idx + 1)
        hi = torch.spmm(adj, input_feat)
        
        if self.variant:
            support = torch.cat([hi, h0], 1)
            r = (1 - self.alpha) * hi + self.alpha * h0
        else:
            support = (1 - self.alpha) * hi + self.alpha * h0
            r = support
            
        output = theta * torch.mm(support, self.weight) + (1 - theta) * r
        
        if self.residual:
            output = output + input_feat
            
        return output
