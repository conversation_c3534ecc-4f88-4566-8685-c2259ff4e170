#!/usr/bin/env python3
"""
增强训练脚本
解决学习效率低下和模式崩溃问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
from pathlib import Path

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedECPELoss(nn.Module):
    """增强的ECPE损失函数，解决模式崩溃问题"""
    
    def __init__(self, token2idx, alpha=0.25, gamma=2.0, diversity_weight=0.1):
        super().__init__()
        self.token2idx = token2idx
        self.pad_idx = token2idx.get('<pad>', 0)
        self.alpha = alpha
        self.gamma = gamma
        self.diversity_weight = diversity_weight
        
        # 情感类别权重（平衡不同情感的预测）
        emotion_tokens = ['surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear', '_NONE']
        self.emotion_ids = [token2idx.get(em, -1) for em in emotion_tokens if em in token2idx]
        
    def forward(self, logits, targets):
        """计算增强损失"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # 1. Focal Loss - 解决类别不平衡
        focal_loss = self._compute_focal_loss(logits, targets)
        
        # 2. 多样性损失 - 防止模式崩溃
        diversity_loss = self._compute_diversity_loss(logits, targets)
        
        # 3. 结构化损失 - 鼓励正确的序列结构
        structure_loss = self._compute_structure_loss(logits, targets)
        
        total_loss = focal_loss + self.diversity_weight * diversity_loss + 0.1 * structure_loss
        
        return total_loss
    
    def _compute_focal_loss(self, logits, targets):
        """计算Focal Loss"""
        logits_flat = logits.view(-1, logits.size(-1))
        targets_flat = targets.view(-1)
        
        # 创建掩码，忽略padding
        mask = targets_flat != self.pad_idx
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        logits_masked = logits_flat[mask]
        targets_masked = targets_flat[mask]
        
        # 计算交叉熵
        ce_loss = F.cross_entropy(logits_masked, targets_masked, reduction='none')
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # Focal Loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()
    
    def _compute_diversity_loss(self, logits, targets):
        """计算多样性损失，防止模式崩溃"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # 计算预测分布的熵
        probs = F.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        
        # 鼓励高熵（多样性）
        diversity_loss = -entropy.mean()
        
        return diversity_loss
    
    def _compute_structure_loss(self, logits, targets):
        """计算结构化损失"""
        batch_size, seq_len, vocab_size = logits.shape
        structure_loss = 0.0
        
        sep_id = self.token2idx.get('<sep>', 3)
        eos_id = self.token2idx.get('<eos>', 2)
        
        # 鼓励在适当位置生成分隔符
        for b in range(batch_size):
            for t in range(seq_len):
                if targets[b, t] == sep_id:
                    # 在应该生成<sep>的位置给予奖励
                    structure_loss -= F.log_softmax(logits[b, t], dim=-1)[sep_id]
                elif targets[b, t] == eos_id:
                    # 在应该结束的位置给予奖励
                    structure_loss -= F.log_softmax(logits[b, t], dim=-1)[eos_id]
        
        return structure_loss / (batch_size * seq_len)


class EnhancedTrainer:
    """增强训练器"""
    
    def __init__(self, model, token2idx, config):
        self.model = model
        self.token2idx = token2idx
        self.config = config
        
        # 增强损失函数
        self.criterion = EnhancedECPELoss(token2idx)
        
        # 分层学习率优化器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 训练状态
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.step_count = 0
        
        # 梯度累积
        self.accumulation_steps = 4
        
    def _create_optimizer(self):
        """创建分层学习率优化器"""
        # PLM使用较小学习率，其他组件使用较大学习率
        plm_params = []
        gnn_params = []
        decoder_params = []
        
        for name, param in self.model.named_parameters():
            if 'plm' in name:
                plm_params.append(param)
            elif 'gnn' in name or 'graph' in name:
                gnn_params.append(param)
            else:
                decoder_params.append(param)
        
        optimizer = torch.optim.AdamW([
            {'params': plm_params, 'lr': 5e-6, 'weight_decay': 0.01},
            {'params': gnn_params, 'lr': 1e-4, 'weight_decay': 0.001},
            {'params': decoder_params, 'lr': 5e-5, 'weight_decay': 0.001}
        ], eps=1e-8, betas=(0.9, 0.999))
        
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=0.7,
            patience=5,
            verbose=True,
            min_lr=1e-7
        )
        return scheduler
    
    def get_teacher_forcing_ratio(self, epoch):
        """获取teacher forcing比率"""
        # 更激进的策略：快速降低但不过低
        initial_ratio = 0.95
        final_ratio = 0.7
        decay_steps = 15
        
        if epoch < decay_steps:
            ratio = final_ratio + (initial_ratio - final_ratio) * (1 - epoch / decay_steps)
        else:
            ratio = final_ratio
            
        return ratio
    
    def train_step(self, batch, epoch):
        """训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            target_seqs = target_seqs.to(device)
            
            if target_seqs.sum() == 0:
                return None
            
            teacher_forcing_ratio = self.get_teacher_forcing_ratio(epoch)
            
            # 前向传播
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            
            # 计算损失
            loss = self.criterion(decoder_outputs, target_seqs)
            
            if torch.isnan(loss) or torch.isinf(loss):
                return None
            
            # 梯度累积
            loss = loss / self.accumulation_steps
            loss.backward()
            
            # 每accumulation_steps步更新一次
            if (self.step_count + 1) % self.accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                
                # 更新参数
                self.optimizer.step()
                self.optimizer.zero_grad()
            
            self.step_count += 1
            
            return loss.item() * self.accumulation_steps
            
        except Exception as e:
            logger.warning(f"训练步骤出错: {e}")
            return None
    
    def should_early_stop(self, current_f1, patience=8):
        """早停判断"""
        if current_f1 > self.best_f1:
            self.best_f1 = current_f1
            self.patience_counter = 0
            return False, True
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience, False


def create_datasets(config):
    """创建数据集"""
    data_path = config.dataset.get_data_path()
    
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        modalities=['text', 'visual', 'audio']
    )
    
    return train_dataset, dev_dataset


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def main():
    """主训练函数"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)
    
    # 增强训练配置
    config.training.batch_size = 4  # 减小批次大小，增加梯度累积
    config.training.epochs = 30
    
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    
    config.print_config()
    print_args(args)
    
    logger.info("🚀 开始增强训练...")
    logger.info("🔧 增强策略:")
    logger.info("  - Focal Loss解决类别不平衡")
    logger.info("  - 多样性损失防止模式崩溃")
    logger.info("  - 分层学习率优化")
    logger.info("  - 梯度累积提高稳定性")
    
    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset, dev_dataset = create_datasets(config)
    
    # 创建词汇表
    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)
    
    logger.info(f"词汇表大小: {config.model.vocab_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    # 创建模型
    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 创建增强训练器
    trainer = EnhancedTrainer(model, token2idx, config)
    
    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.6
    
    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        teacher_forcing_ratio = trainer.get_teacher_forcing_ratio(epoch)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.3f}")
        
        for batch in tqdm(train_loader, desc="训练"):
            loss = trainer.train_step(batch, epoch)
            if loss is not None:
                train_loss += loss
                train_batches += 1
        
        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")
        
        # 验证阶段（每2个epoch验证一次）
        if (epoch + 1) % 2 == 0:
            model.eval()
            val_loss = 0.0
            val_batches = 0
            all_predictions = []
            all_targets = []
            
            with torch.no_grad():
                for batch in tqdm(dev_loader, desc="验证"):
                    try:
                        target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                        target_seqs = target_seqs.to(device)
                        
                        decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                        loss = trainer.criterion(decoder_outputs, target_seqs)
                        val_loss += loss.item()
                        val_batches += 1
                        
                        # 简单贪婪解码
                        predictions = torch.argmax(decoder_outputs, dim=-1)
                        all_predictions.append(predictions)
                        all_targets.append(target_seqs)
                        
                    except Exception as e:
                        logger.warning(f"验证批次出错: {e}")
                        continue
            
            avg_val_loss = val_loss / max(1, val_batches)
            
            # 计算指标
            if all_predictions and all_targets:
                combined_predictions = torch.cat(all_predictions, dim=0)
                combined_targets = torch.cat(all_targets, dim=0)
                
                post_processor = UnifiedPostProcessor(mode='optimal')
                metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
            else:
                metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
            
            logger.info(f"验证损失: {avg_val_loss:.4f}")
            logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
            
            # 学习率调度
            trainer.scheduler.step(metrics['f1'])
            
            # 早停检查
            should_stop, is_best = trainer.should_early_stop(metrics['f1'])
            
            if is_best:
                best_f1 = metrics['f1']
                best_metrics = metrics.copy()
                logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")
                
                # 保存模型
                output_dir = Path(config.experiment.output_dir)
                output_dir.mkdir(exist_ok=True)
                model_save_path = output_dir / f"best_enhanced_model_{config.dataset.name}.pt"
                torch.save(model.state_dict(), model_save_path)
                logger.info(f"  模型已保存到: {model_save_path}")
            
            if should_stop:
                logger.info("早停触发")
                break
    
    logger.info(f"\n🎯 增强训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    
    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    main()
