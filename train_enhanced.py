#!/usr/bin/env python3
"""
增强训练脚本
解决学习效率低下和模式崩溃问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
from pathlib import Path
from transformers import AutoTokenizer
import json

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from utils import compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_roberta_tokenizer_and_vocab(plm_path="/root/roberta"):
    """
    创建RoBERTa tokenizer和扩展词汇表
    """
    # 加载RoBERTa tokenizer
    tokenizer = AutoTokenizer.from_pretrained(plm_path)

    # 定义ECPE任务的特殊token
    special_tokens = {
        'additional_special_tokens': [
            '<ecpe_start>', '<ecpe_end>', '<pair_sep>',
            '<emotion>', '<cause>', '<utt_sep>'
        ]
    }

    # 添加话语ID tokens
    utterance_tokens = [f'<utt_{i:03d}>' for i in range(100)]
    special_tokens['additional_special_tokens'].extend(utterance_tokens)

    # 添加情感类别tokens
    emotion_tokens = ['<surprise>', '<joy>', '<sadness>', '<neutral>',
                     '<disgust>', '<anger>', '<fear>', '<none_emotion>']
    special_tokens['additional_special_tokens'].extend(emotion_tokens)

    # 添加特殊token到tokenizer
    num_added_tokens = tokenizer.add_special_tokens(special_tokens)
    logger.info(f"添加了 {num_added_tokens} 个特殊token")

    # 创建token映射
    vocab = tokenizer.get_vocab()
    token2idx = vocab
    idx2token = {idx: token for token, idx in vocab.items()}

    logger.info(f"扩展后词汇表大小: {len(token2idx)}")

    return tokenizer, token2idx, idx2token


def prepare_roberta_target_sequences(batch, tokenizer, max_length=50):
    """
    使用RoBERTa tokenizer准备目标序列
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    try:
        # 获取批次信息 - 更安全的方式
        if isinstance(batch, (list, tuple)) and len(batch) > 0:
            # 尝试从不同位置获取ECPE对信息
            ecpe_pairs = None
            batch_size = None

            # 检查batch结构
            if len(batch) >= 6 and hasattr(batch[5], '__len__'):
                ecpe_pairs = batch[5]
                batch_size = len(ecpe_pairs) if hasattr(ecpe_pairs, '__len__') else 1
            elif len(batch) >= 1:
                # 从第一个元素推断批次大小
                first_item = batch[0]
                if hasattr(first_item, 'size'):
                    batch_size = first_item.size(0)
                elif hasattr(first_item, '__len__'):
                    batch_size = len(first_item)
                else:
                    batch_size = 1
            else:
                batch_size = 1

            # 如果没有找到ECPE对，创建空的
            if ecpe_pairs is None:
                ecpe_pairs = [[] for _ in range(batch_size)]
        else:
            batch_size = 1
            ecpe_pairs = [[]]

        target_sequences = []

        for b in range(batch_size):
            # 安全地获取当前样本的对
            if isinstance(ecpe_pairs, (list, tuple)) and b < len(ecpe_pairs):
                pairs = ecpe_pairs[b] if ecpe_pairs[b] is not None else []
            else:
                pairs = []

            # 构建目标序列文本
            sequence_text = "<ecpe_start>"

            # 处理每个情感-原因对
            if isinstance(pairs, (list, tuple)):
                for pair in pairs:
                    if isinstance(pair, (list, tuple)) and len(pair) >= 3:
                        try:
                            emotion_utt, emotion, cause_utt = pair[0], pair[1], pair[2]

                            # 确保是数字格式
                            if isinstance(emotion_utt, str) and emotion_utt.startswith('utt_'):
                                emotion_utt = int(emotion_utt.split('_')[1])
                            elif not isinstance(emotion_utt, int):
                                emotion_utt = 0

                            if isinstance(cause_utt, str) and cause_utt.startswith('utt_'):
                                cause_utt = int(cause_utt.split('_')[1])
                            elif not isinstance(cause_utt, int):
                                cause_utt = 0

                            # 格式：<utt_xxx> <emotion> <utt_xxx> <pair_sep>
                            pair_text = f" <utt_{emotion_utt:03d}> <{emotion}> <utt_{cause_utt:03d}> <pair_sep>"
                            sequence_text += pair_text
                        except (ValueError, IndexError, TypeError):
                            continue

            sequence_text += " <ecpe_end>"

            # 使用tokenizer编码
            try:
                encoded = tokenizer.encode(
                    sequence_text,
                    max_length=max_length,
                    padding='max_length',
                    truncation=True,
                    return_tensors='pt'
                )
                target_sequences.append(encoded.squeeze(0))
            except Exception as e:
                logger.warning(f"编码序列失败: {e}, 使用默认序列")
                # 创建默认序列
                default_seq = torch.full((max_length,), tokenizer.pad_token_id, dtype=torch.long)
                default_seq[0] = tokenizer.encode("<ecpe_start>", add_special_tokens=False)[0]
                default_seq[1] = tokenizer.encode("<ecpe_end>", add_special_tokens=False)[0]
                target_sequences.append(default_seq)

        # 堆叠成批次
        if target_sequences:
            target_tensor = torch.stack(target_sequences).to(device)
        else:
            # 创建空的目标张量
            target_tensor = torch.full((batch_size, max_length), tokenizer.pad_token_id,
                                     dtype=torch.long, device=device)

        return target_tensor

    except Exception as e:
        logger.warning(f"准备目标序列失败: {e}, 返回空序列")
        # 返回安全的默认值
        batch_size = 1
        if isinstance(batch, (list, tuple)) and len(batch) > 0:
            first_item = batch[0]
            if hasattr(first_item, 'size'):
                batch_size = first_item.size(0)

        return torch.full((batch_size, max_length), tokenizer.pad_token_id,
                         dtype=torch.long, device=device)


def decode_roberta_predictions(predictions, tokenizer, post_processor):
    """
    解码RoBERTa预测结果
    """
    batch_size = predictions.size(0)
    decoded_pairs_batch = []

    for b in range(batch_size):
        # 解码token序列
        pred_tokens = predictions[b].cpu().numpy()
        decoded_text = tokenizer.decode(pred_tokens, skip_special_tokens=False)

        # 解析ECPE对
        pairs = parse_ecpe_sequence(decoded_text)
        decoded_pairs_batch.append(pairs)

    return decoded_pairs_batch


def parse_ecpe_sequence(sequence_text):
    """
    解析ECPE序列文本，提取情感-原因对
    """
    pairs = []

    # 查找<ecpe_start>和<ecpe_end>之间的内容
    start_marker = "<ecpe_start>"
    end_marker = "<ecpe_end>"

    start_idx = sequence_text.find(start_marker)
    end_idx = sequence_text.find(end_marker)

    if start_idx == -1 or end_idx == -1:
        return pairs

    content = sequence_text[start_idx + len(start_marker):end_idx].strip()

    # 按<pair_sep>分割对
    pair_segments = content.split("<pair_sep>")

    for segment in pair_segments:
        segment = segment.strip()
        if not segment:
            continue

        # 解析格式：<utt_xxx> <emotion> <utt_xxx>
        tokens = segment.split()
        if len(tokens) >= 3:
            try:
                # 提取话语ID
                utt1 = tokens[0].replace('<utt_', '').replace('>', '')
                emotion = tokens[1].replace('<', '').replace('>', '')
                utt2 = tokens[2].replace('<utt_', '').replace('>', '')

                # 转换为数字格式
                emotion_utt = int(utt1) if utt1.isdigit() else 0
                cause_utt = int(utt2) if utt2.isdigit() else 0

                pairs.append((f'utt_{emotion_utt:03d}', emotion, f'utt_{cause_utt:03d}'))

            except (ValueError, IndexError):
                continue

    return pairs


def compute_roberta_ecpe_metrics(predictions, targets, tokenizer):
    """
    计算使用RoBERTa tokenizer的ECPE指标
    """
    batch_size = predictions.size(0)

    all_pred_pairs = []
    all_target_pairs = []

    for b in range(batch_size):
        # 解码预测序列
        pred_tokens = predictions[b].cpu().numpy()
        pred_text = tokenizer.decode(pred_tokens, skip_special_tokens=False)
        pred_pairs = parse_ecpe_sequence(pred_text)
        all_pred_pairs.extend(pred_pairs)

        # 解码目标序列
        target_tokens = targets[b].cpu().numpy()
        target_text = tokenizer.decode(target_tokens, skip_special_tokens=False)
        target_pairs = parse_ecpe_sequence(target_text)
        all_target_pairs.extend(target_pairs)

    # 计算精确率、召回率和F1分数
    if len(all_pred_pairs) == 0:
        precision = 0.0
    else:
        correct_pairs = len(set(all_pred_pairs) & set(all_target_pairs))
        precision = correct_pairs / len(all_pred_pairs)

    if len(all_target_pairs) == 0:
        recall = 0.0
    else:
        correct_pairs = len(set(all_pred_pairs) & set(all_target_pairs))
        recall = correct_pairs / len(all_target_pairs)

    if precision + recall == 0:
        f1 = 0.0
    else:
        f1 = 2 * precision * recall / (precision + recall)

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'pred_pairs': len(all_pred_pairs),
        'target_pairs': len(all_target_pairs),
        'correct_pairs': len(set(all_pred_pairs) & set(all_target_pairs))
    }


class EnhancedECPELoss(nn.Module):
    """增强的ECPE损失函数，适配RoBERTa tokenizer"""

    def __init__(self, tokenizer, alpha=0.25, gamma=2.0, diversity_weight=0.1):
        super().__init__()
        self.tokenizer = tokenizer
        self.pad_idx = tokenizer.pad_token_id
        self.alpha = alpha
        self.gamma = gamma
        self.diversity_weight = diversity_weight

        # 获取特殊token的ID
        vocab = tokenizer.get_vocab()
        self.ecpe_start_id = vocab.get('<ecpe_start>', -1)
        self.ecpe_end_id = vocab.get('<ecpe_end>', -1)
        self.pair_sep_id = vocab.get('<pair_sep>', -1)

        # 情感token IDs
        emotion_tokens = ['<surprise>', '<joy>', '<sadness>', '<neutral>',
                         '<disgust>', '<anger>', '<fear>', '<none_emotion>']
        self.emotion_ids = [vocab.get(em, -1) for em in emotion_tokens if vocab.get(em, -1) != -1]
        
    def forward(self, logits, targets):
        """计算增强损失"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # 1. Focal Loss - 解决类别不平衡
        focal_loss = self._compute_focal_loss(logits, targets)
        
        # 2. 多样性损失 - 防止模式崩溃
        diversity_loss = self._compute_diversity_loss(logits, targets)
        
        # 3. 结构化损失 - 鼓励正确的序列结构
        structure_loss = self._compute_structure_loss(logits, targets)
        
        total_loss = focal_loss + self.diversity_weight * diversity_loss + 0.1 * structure_loss
        
        return total_loss
    
    def _compute_focal_loss(self, logits, targets):
        """计算Focal Loss"""
        logits_flat = logits.view(-1, logits.size(-1))
        targets_flat = targets.view(-1)
        
        # 创建掩码，忽略padding
        mask = targets_flat != self.pad_idx
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        logits_masked = logits_flat[mask]
        targets_masked = targets_flat[mask]
        
        # 计算交叉熵
        ce_loss = F.cross_entropy(logits_masked, targets_masked, reduction='none')
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # Focal Loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()
    
    def _compute_diversity_loss(self, logits, targets):
        """计算多样性损失，防止模式崩溃"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # 计算预测分布的熵
        probs = F.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        
        # 鼓励高熵（多样性）
        diversity_loss = -entropy.mean()
        
        return diversity_loss
    
    def _compute_structure_loss(self, logits, targets):
        """计算结构化损失"""
        batch_size, seq_len, vocab_size = logits.shape
        structure_loss = 0.0
        count = 0

        # 鼓励在适当位置生成特殊token
        special_tokens = [self.ecpe_start_id, self.ecpe_end_id, self.pair_sep_id]

        for b in range(batch_size):
            for t in range(seq_len):
                target_token = targets[b, t].item()
                if target_token in special_tokens and target_token != -1:
                    # 在应该生成特殊token的位置给予奖励
                    log_probs = F.log_softmax(logits[b, t], dim=-1)
                    structure_loss -= log_probs[target_token]
                    count += 1

        return structure_loss / max(count, 1)


class EnhancedTrainer:
    """增强训练器"""

    def __init__(self, model, tokenizer, config):
        self.model = model
        self.tokenizer = tokenizer
        self.config = config

        # 增强损失函数
        self.criterion = EnhancedECPELoss(tokenizer)
        
        # 分层学习率优化器
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # 训练状态
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.step_count = 0
        
        # 梯度累积
        self.accumulation_steps = 4
        
    def _create_optimizer(self):
        """创建分层学习率优化器"""
        # PLM使用较小学习率，其他组件使用较大学习率
        plm_params = []
        gnn_params = []
        decoder_params = []
        
        for name, param in self.model.named_parameters():
            if 'plm' in name:
                plm_params.append(param)
            elif 'gnn' in name or 'graph' in name:
                gnn_params.append(param)
            else:
                decoder_params.append(param)
        
        optimizer = torch.optim.AdamW([
            {'params': plm_params, 'lr': 5e-6, 'weight_decay': 0.01},
            {'params': gnn_params, 'lr': 1e-4, 'weight_decay': 0.001},
            {'params': decoder_params, 'lr': 5e-5, 'weight_decay': 0.001}
        ], eps=1e-8, betas=(0.9, 0.999))
        
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=0.7,
            patience=5,
            verbose=True,
            min_lr=1e-7
        )
        return scheduler
    
    def get_teacher_forcing_ratio(self, epoch):
        """获取teacher forcing比率"""
        # 更激进的策略：快速降低但不过低
        initial_ratio = 0.95
        final_ratio = 0.7
        decay_steps = 15
        
        if epoch < decay_steps:
            ratio = final_ratio + (initial_ratio - final_ratio) * (1 - epoch / decay_steps)
        else:
            ratio = final_ratio
            
        return ratio
    
    def train_step(self, batch, epoch):
        """训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_roberta_target_sequences(batch, self.tokenizer, max_length=50)
            target_seqs = target_seqs.to(device)

            if target_seqs.sum() == 0:
                return None

            teacher_forcing_ratio = self.get_teacher_forcing_ratio(epoch)

            # 前向传播
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)

            # 计算损失
            loss = self.criterion(decoder_outputs, target_seqs)

            if torch.isnan(loss) or torch.isinf(loss):
                return None

            # 梯度累积
            loss = loss / self.accumulation_steps
            loss.backward()

            # 每accumulation_steps步更新一次
            if (self.step_count + 1) % self.accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

                # 更新参数
                self.optimizer.step()
                self.optimizer.zero_grad()

            self.step_count += 1

            return loss.item() * self.accumulation_steps

        except Exception as e:
            logger.warning(f"训练步骤出错: {e}")
            return None
    
    def should_early_stop(self, current_f1, patience=8):
        """早停判断"""
        if current_f1 > self.best_f1:
            self.best_f1 = current_f1
            self.patience_counter = 0
            return False, True
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience, False


def create_datasets(config):
    """创建数据集"""
    data_path = config.dataset.get_data_path()
    
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        modalities=['text', 'visual', 'audio']
    )
    
    return train_dataset, dev_dataset


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def main():
    """主训练函数"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)
    
    # 增强训练配置
    config.training.batch_size = 4  # 减小批次大小，增加梯度累积
    config.training.epochs = 30
    
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    
    config.print_config()
    print_args(args)
    
    logger.info("🚀 开始增强训练...")
    logger.info("🔧 增强策略:")
    logger.info("  - Focal Loss解决类别不平衡")
    logger.info("  - 多样性损失防止模式崩溃")
    logger.info("  - 分层学习率优化")
    logger.info("  - 梯度累积提高稳定性")
    
    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset, dev_dataset = create_datasets(config)
    
    # 创建RoBERTa tokenizer和扩展词汇表
    tokenizer, token2idx, idx2token = create_roberta_tokenizer_and_vocab(config.model.plm_path)
    config.model.vocab_size = len(token2idx)

    logger.info(f"扩展后词汇表大小: {config.model.vocab_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    # 创建模型
    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)

    # 调整模型的嵌入层大小以匹配扩展词汇表
    if hasattr(model, 'plm'):
        model.plm.resize_token_embeddings(len(tokenizer))

    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建增强训练器
    trainer = EnhancedTrainer(model, tokenizer, config)
    
    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.6
    
    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        teacher_forcing_ratio = trainer.get_teacher_forcing_ratio(epoch)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.3f}")
        
        for batch in tqdm(train_loader, desc="训练"):
            loss = trainer.train_step(batch, epoch)
            if loss is not None:
                train_loss += loss
                train_batches += 1
        
        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")
        
        # 验证阶段（每2个epoch验证一次）
        if (epoch + 1) % 2 == 0:
            model.eval()
            val_loss = 0.0
            val_batches = 0
            all_predictions = []
            all_targets = []
            
            with torch.no_grad():
                for batch in tqdm(dev_loader, desc="验证"):
                    try:
                        target_seqs = prepare_roberta_target_sequences(batch, tokenizer, max_length=50)
                        target_seqs = target_seqs.to(device)

                        decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                        loss = trainer.criterion(decoder_outputs, target_seqs)
                        val_loss += loss.item()
                        val_batches += 1

                        # 简单贪婪解码
                        predictions = torch.argmax(decoder_outputs, dim=-1)
                        all_predictions.append(predictions)
                        all_targets.append(target_seqs)

                    except Exception as e:
                        logger.warning(f"验证批次出错: {e}")
                        continue
            
            avg_val_loss = val_loss / max(1, val_batches)
            
            # 计算指标
            if all_predictions and all_targets:
                combined_predictions = torch.cat(all_predictions, dim=0)
                combined_targets = torch.cat(all_targets, dim=0)

                # 使用RoBERTa解码方式计算指标
                metrics = compute_roberta_ecpe_metrics(
                    combined_predictions, combined_targets, tokenizer
                )
            else:
                metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
            
            logger.info(f"验证损失: {avg_val_loss:.4f}")
            logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
            
            # 学习率调度
            trainer.scheduler.step(metrics['f1'])
            
            # 早停检查
            should_stop, is_best = trainer.should_early_stop(metrics['f1'])
            
            if is_best:
                best_f1 = metrics['f1']
                best_metrics = metrics.copy()
                logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")
                
                # 保存模型
                output_dir = Path(config.experiment.output_dir)
                output_dir.mkdir(exist_ok=True)
                model_save_path = output_dir / f"best_enhanced_model_{config.dataset.name}.pt"
                torch.save(model.state_dict(), model_save_path)
                logger.info(f"  模型已保存到: {model_save_path}")
            
            if should_stop:
                logger.info("早停触发")
                break
    
    logger.info(f"\n🎯 增强训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    
    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    main()
