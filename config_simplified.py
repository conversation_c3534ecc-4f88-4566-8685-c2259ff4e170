#!/usr/bin/env python3
"""
简化的配置文件
专注于稳定训练的核心配置
"""

import os
from typing import Dict, List
from dataclasses import dataclass, field


@dataclass
class SimplifiedModelConfig:
    """简化的模型配置"""
    # 预训练语言模型
    plm_model: str = "roberta"
    plm_path: str = "/userdata2/fengweijie/roberta"
    
    # 核心模型参数
    hidden_size: int = 256
    gnn_layers: int = 2
    decoder_layers: int = 2
    num_attention_heads: int = 8
    dropout: float = 0.1
    
    # 图编码器配置
    graph_encoder_type: str = "gat"
    gat_layers: int = 2
    gat_heads: int = 4
    gat_dropout: float = 0.1
    
    # 序列生成配置
    max_decode_length: int = 50
    max_target_length: int = 50
    vocab_size: int = 0  # 运行时设置
    
    # 多模态配置
    use_multimodal: bool = True
    modalities: List[str] = field(default_factory=lambda: ['text', 'visual', 'audio'])
    visual_dim: int = 4096
    audio_dim: int = 6373
    normalize_multimodal_features: bool = True
    
    # 其他必需配置
    freeze_plm: bool = False
    speakers: List[str] = field(default_factory=lambda: ['_NONE'])


@dataclass
class SimplifiedTrainingConfig:
    """简化的训练配置"""
    # 基础训练参数
    batch_size: int = 8
    learning_rate: float = 2e-5
    epochs: int = 15
    weight_decay: float = 0.01
    
    # 稳定性参数
    gradient_clip_norm: float = 1.0
    warmup_steps: int = 100
    
    # 早停配置
    early_stopping: bool = True
    patience: int = 5
    
    # 损失函数配置
    loss_type: str = "simplified"  # 'simplified', 'focal', 'label_smoothing', 'structure_aware'
    label_smoothing: float = 0.1
    
    # 设备配置
    device: str = "cuda"
    dataloader_num_workers: int = 2


@dataclass
class SimplifiedDatasetConfig:
    """简化的数据集配置"""
    name: str = "meld"
    root_path: str = field(default_factory=lambda: os.path.dirname(os.path.abspath(__file__)))
    
    # 数据集路径
    dataset_paths: Dict[str, str] = field(default_factory=lambda: {
        "meld": "data/meld/",
        "iemocap": "data/iemocap/"
    })
    
    # 情感类别
    emotion_categories: Dict[str, List[str]] = field(default_factory=lambda: {
        "meld": ["_NONE", "surprise", "joy", "sadness", "neutral", "disgust", "anger", "fear"],
        "iemocap": ["_NONE", "anger", "happiness", "sadness", "neutral", "excitement", "frustration"]
    })
    
    # 说话者
    speakers: Dict[str, List[str]] = field(default_factory=lambda: {
        "meld": ["_NONE", "Chandler", "Joey", "Ross", "Rachel", "Monica", "Phoebe"],
        "iemocap": ["_NONE", "Ses01F", "Ses01M", "Ses02F", "Ses02M", "Ses03F", "Ses03M", 
                   "Ses04F", "Ses04M", "Ses05F", "Ses05M"]
    })
    
    def get_data_path(self) -> str:
        """获取数据路径"""
        relative_path = self.dataset_paths.get(self.name, self.dataset_paths["meld"])
        return os.path.join(self.root_path, relative_path)
    
    def get_emotion_categories(self) -> List[str]:
        """获取情感类别"""
        return self.emotion_categories.get(self.name, self.emotion_categories["meld"])
    
    def get_speakers(self) -> List[str]:
        """获取说话者"""
        return self.speakers.get(self.name, self.speakers["meld"])


@dataclass
class SimplifiedExperimentConfig:
    """简化的实验配置"""
    experiment_name: str = "simplified_graph2ecpe"
    output_dir: str = "output"
    seed: int = 42
    log_level: str = "INFO"


class SimplifiedConfig:
    """简化的统一配置类"""
    
    def __init__(self, dataset_name: str = "meld"):
        self.dataset = SimplifiedDatasetConfig(name=dataset_name)
        self.model = SimplifiedModelConfig()
        self.training = SimplifiedTrainingConfig()
        self.experiment = SimplifiedExperimentConfig()
        
        # 根据数据集调整配置
        self._adjust_for_dataset()
    
    def _adjust_for_dataset(self):
        """根据数据集调整配置"""
        if self.dataset.name == "iemocap":
            # IEMOCAP数据集调整
            self.training.batch_size = 6
            self.training.learning_rate = 1e-5
        
        # 设置说话者
        self.model.speakers = self.dataset.get_speakers()
    
    def update_from_args(self, args):
        """从命令行参数更新配置"""
        # 数据集配置
        if hasattr(args, 'dataset') and args.dataset != self.dataset.name:
            self.dataset.name = args.dataset
            self._adjust_for_dataset()
        
        # 模型配置
        if hasattr(args, 'hidden_size'):
            self.model.hidden_size = args.hidden_size
        if hasattr(args, 'gnn_layers'):
            self.model.gnn_layers = args.gnn_layers
        if hasattr(args, 'decoder_layers'):
            self.model.decoder_layers = args.decoder_layers
        
        # 训练配置
        if hasattr(args, 'batch_size'):
            self.training.batch_size = args.batch_size
        if hasattr(args, 'learning_rate'):
            self.training.learning_rate = args.learning_rate
        if hasattr(args, 'epochs'):
            self.training.epochs = args.epochs
        if hasattr(args, 'loss_type'):
            self.training.loss_type = args.loss_type
        
        # 实验配置
        if hasattr(args, 'output_dir'):
            self.experiment.output_dir = args.output_dir
        if hasattr(args, 'seed'):
            self.experiment.seed = args.seed
    
    def print_config(self):
        """打印配置信息"""
        print("=" * 50)
        print("🔧 简化配置信息")
        print("=" * 50)
        
        print(f"\n📊 数据集配置:")
        print(f"  数据集: {self.dataset.name}")
        print(f"  数据路径: {self.dataset.get_data_path()}")
        print(f"  情感类别: {len(self.dataset.get_emotion_categories())} 个")
        
        print(f"\n🤖 模型配置:")
        print(f"  隐藏层大小: {self.model.hidden_size}")
        print(f"  GNN层数: {self.model.gnn_layers}")
        print(f"  解码器层数: {self.model.decoder_layers}")
        print(f"  多模态: {self.model.use_multimodal}")
        
        print(f"\n🏋️ 训练配置:")
        print(f"  批次大小: {self.training.batch_size}")
        print(f"  学习率: {self.training.learning_rate}")
        print(f"  训练轮数: {self.training.epochs}")
        print(f"  损失函数: {self.training.loss_type}")
        
        print(f"\n🧪 实验配置:")
        print(f"  实验名称: {self.experiment.experiment_name}")
        print(f"  输出目录: {self.experiment.output_dir}")
        print(f"  随机种子: {self.experiment.seed}")
        
        print("=" * 50)


# 全局配置实例
_global_simplified_config = None

def get_simplified_config() -> SimplifiedConfig:
    """获取全局简化配置实例"""
    global _global_simplified_config
    if _global_simplified_config is None:
        _global_simplified_config = SimplifiedConfig()
    return _global_simplified_config

def set_simplified_config(config: SimplifiedConfig):
    """设置全局简化配置实例"""
    global _global_simplified_config
    _global_simplified_config = config

def create_simplified_config(dataset_name: str = "meld") -> SimplifiedConfig:
    """创建简化配置实例"""
    return SimplifiedConfig(dataset_name=dataset_name)


# 预设配置
def get_lightweight_config() -> SimplifiedConfig:
    """获取轻量级配置"""
    config = SimplifiedConfig()
    config.model.hidden_size = 128
    config.model.gnn_layers = 1
    config.model.decoder_layers = 1
    config.training.batch_size = 16
    config.training.epochs = 10
    return config

def get_performance_config() -> SimplifiedConfig:
    """获取高性能配置"""
    config = SimplifiedConfig()
    config.model.hidden_size = 512
    config.model.gnn_layers = 3
    config.model.decoder_layers = 3
    config.training.batch_size = 4
    config.training.epochs = 20
    config.training.learning_rate = 1e-5
    return config
