import numpy as np
import torch
from torch_geometric.utils import dense_to_sparse


def batch_graphify(utterance_features, lengths, speaker_tensor, edge_indices_batch, edge_types_batch, edge_norms_batch, device):
    """
    将对话转换为图结构
    Args:
        utterance_features: 节点特征 [batch_size, max_len, hidden_dim]
        lengths: 每个对话的实际长度 [batch_size]
        speaker_tensor: 说话者ID [batch_size, max_len]
        edge_indices_batch: 每个对话的边索引列表
        edge_types_batch: 每个对话的边类型列表
        edge_norms_batch: 每个对话的边权重列表
        device: 设备
    Returns:
        node_features: 节点特征 [total_nodes, hidden_dim]
        edge_index: 边索引 [2, total_edges]
        edge_type: 边类型 [total_edges]
        edge_norm: 边权重 [total_edges]
        batch: 每个节点所属的batch index [total_nodes]
    """
    # 基本参数检查
    batch_size = utterance_features.size(0)
    max_len = utterance_features.size(1)
    hidden_dim = utterance_features.size(2)
    
    # 创建累计长度数组，用于计算全局节点索引的偏移量
    cumulative_lengths = [0]
    for i in range(batch_size):
        if i < lengths.size(0):
            cumulative_lengths.append(cumulative_lengths[-1] + min(lengths[i].item(), max_len))
        else:
            cumulative_lengths.append(cumulative_lengths[-1])
    
    # 初始化结果列表
    all_node_features = []
    all_edge_indices = []
    all_edge_types = []
    all_edge_norms = []
    all_batch_indices = []
    
    # 为每个对话处理节点和边
    for b in range(batch_size):
        # 获取当前对话的有效长度
        if b < lengths.size(0):
            # 确保长度在有效范围内
            cur_len = min(int(lengths[b].item()), max_len)
            cur_len = max(1, cur_len)  # 至少有一个节点
        else:
            cur_len = 1  # 默认最小长度
        
        # 提取当前对话的节点特征
        batch_nodes = utterance_features[b, :cur_len, :]
        all_node_features.append(batch_nodes)
        
        # 记录节点的batch索引
        batch_indices = torch.full((cur_len,), b, dtype=torch.long, device=device)
        all_batch_indices.append(batch_indices)
        
        # 处理边数据
        start_idx = cumulative_lengths[b]  # 当前batch在全局中的起始索引
        
        # 检查是否有边数据
        if b < len(edge_indices_batch) and edge_indices_batch[b]:
            # 获取当前batch的边
            cur_edges = edge_indices_batch[b]
            cur_types = edge_types_batch[b] if b < len(edge_types_batch) else []
            cur_norms = edge_norms_batch[b] if b < len(edge_norms_batch) else []
            
            # 验证和处理每条边
            for i, (src, tgt) in enumerate(cur_edges):
                # 检查边的索引是否有效
                if 0 <= src < cur_len and 0 <= tgt < cur_len:
                    # 计算全局索引
                    global_src = src + start_idx
                    global_tgt = tgt + start_idx
                    
                    # 添加边
                    all_edge_indices.append([global_src, global_tgt])
                    
                    # 添加边类型 (安全地获取)
                    if i < len(cur_types):
                        all_edge_types.append(cur_types[i])
                    else:
                        all_edge_types.append(0)  # 默认类型
                    
                    # 添加边权重 (安全地获取)
                    if i < len(cur_norms):
                        all_edge_norms.append(cur_norms[i])
                    else:
                        all_edge_norms.append(1.0)  # 默认权重
    
    # 合并所有节点和批次索引
    if all_node_features:
        node_features = torch.cat(all_node_features, dim=0).to(device)
        batch_indices = torch.cat(all_batch_indices, dim=0).to(device)
    else:
        # 创建空的默认值
        node_features = torch.zeros((1, hidden_dim), device=device)
        batch_indices = torch.zeros(1, dtype=torch.long, device=device)
    
    # 处理边数据
    if all_edge_indices:
        edge_index = torch.tensor(all_edge_indices, dtype=torch.long, device=device).t().contiguous()
        edge_type = torch.tensor(all_edge_types, dtype=torch.long, device=device)
        edge_norm = torch.tensor(all_edge_norms, dtype=torch.float, device=device)
    else:
        # 创建空的默认值 - 至少有一个自环边避免图网络错误
        edge_index = torch.tensor([[0, 0]], dtype=torch.long, device=device).t().contiguous()
        edge_type = torch.zeros(1, dtype=torch.long, device=device)
        edge_norm = torch.ones(1, dtype=torch.float, device=device)
    
    return node_features, edge_index, edge_norm, edge_type, batch_indices


def create_utterance_features(dialog_tokens, dialog_uttid, dialog_mask, hidden_size, device):
    """
    从对话token序列创建话语级别的特征
    Args:
        dialog_tokens: 对话token序列 [batch_size, seq_len, token_dim]
        dialog_uttid: 每个token对应的话语id [batch_size, seq_len]
        dialog_mask: token mask [batch_size, seq_len]
        hidden_size: 隐藏层维度
        device: 设备
    Returns:
        utterance_features: 话语级别的特征 [batch_size, max_n_utt, token_dim]
        lengths: 每个对话的实际话语数量 [batch_size]
    """
    batch_size = dialog_tokens.size(0)
    token_dim = dialog_tokens.size(2)  # 获取token的实际维度
    
    # 计算每个对话的最大话语ID
    max_uttid_per_batch = []
    for b in range(batch_size):
        # 使用mask获取有效的话语ID
        valid_mask = dialog_mask[b] == 1
        if valid_mask.sum() > 0:
            valid_uttids = dialog_uttid[b][valid_mask]
            if valid_uttids.numel() > 0:
                max_uttid = valid_uttids.max().item()
                max_uttid_per_batch.append(max_uttid)
            else:
                max_uttid_per_batch.append(1)  # 默认至少有一个话语
        else:
            max_uttid_per_batch.append(1)  # 默认至少有一个话语
    
    # 计算全局最大话语ID
    max_n_utt = max(max_uttid_per_batch) if max_uttid_per_batch else 1
    
    # 初始化话语特征和长度
    utterance_features = torch.zeros(batch_size, max_n_utt, token_dim).to(device)
    lengths = torch.zeros(batch_size, dtype=torch.long).to(device)
    
    # 为每个对话计算话语特征
    for b in range(batch_size):
        # 获取当前对话的最大话语ID
        cur_max_uttid = max_uttid_per_batch[b]
        valid_utts = 0
        
        # 为每个话语收集token并计算特征
        for u in range(1, cur_max_uttid + 1):  # 跳过0（通常是CLS token）
            # 找出属于该话语的所有token
            utt_indices = (dialog_uttid[b] == u) & (dialog_mask[b] == 1)
            
            # 如果存在该话语的token
            if torch.sum(utt_indices) > 0:
                # 提取该话语的token表示并进行平均池化
                utt_tokens = dialog_tokens[b, utt_indices]
                utterance_features[b, u-1] = torch.mean(utt_tokens, dim=0)
                valid_utts += 1
        
        # 存储实际有效的话语数量
        lengths[b] = max(1, valid_utts)  # 确保至少有一个有效话语
    
    return utterance_features, lengths


def edge_perms(length, window_past, window_future):
    """
    生成时序边的排列
    Args:
        length: 对话长度
        window_past: 过去窗口大小
        window_future: 未来窗口大小
    Returns:
        list of tuples: [(source_node, target_node), ...]
    """
    all_perms = set()
    array = np.arange(length)
    for j in range(length):
        perms = set()
        eff_array = array[max(0, j - window_past):min(length, j + window_future + 1)]
        for item in eff_array:
            if j != item:
                perms.add((j, item))
        all_perms = all_perms.union(perms)
    return list(all_perms)