#!/usr/bin/env python3
"""
修复版稳定训练脚本
解决梯度爆炸和数值不稳定问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
from pathlib import Path

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StableLoss(nn.Module):
    """稳定的损失函数，防止数值爆炸"""
    
    def __init__(self, token2idx):
        super().__init__()
        self.pad_idx = token2idx.get('<pad>', 0)
        self.criterion = nn.CrossEntropyLoss(ignore_index=self.pad_idx)
    
    def forward(self, logits, targets):
        batch_size, seq_len, vocab_size = logits.shape
        
        # 检查并修复异常值
        logits = torch.clamp(logits, min=-50, max=50)  # 限制logits范围
        
        loss = self.criterion(logits.view(-1, vocab_size), targets.view(-1))
        
        # 限制损失的最大值
        loss = torch.clamp(loss, max=20.0)
        
        return loss


class StableTrainer:
    """稳定训练器，专门解决梯度爆炸问题"""
    
    def __init__(self, model, token2idx, config):
        self.model = model
        self.token2idx = token2idx
        self.config = config
        
        # 稳定损失函数
        self.criterion = StableLoss(token2idx)
        
        # 初始化模型权重
        self._initialize_model_weights()
        
        # 极保守的优化器设置
        self.optimizer = self._create_stable_optimizer()
        self.scheduler = self._create_stable_scheduler()
        
        # 训练状态
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.step_count = 0
        
        # 稳定性参数
        self.max_grad_norm = 0.5  # 非常严格的梯度裁剪
        self.accumulation_steps = 8  # 增加梯度累积
    
    def _initialize_model_weights(self):
        """重新初始化模型权重，使用更保守的初始化"""
        logger.info("🔧 重新初始化模型权重...")
        
        for name, param in self.model.named_parameters():
            if 'weight' in name:
                if 'modal_projections' in name:
                    # 多模态投影层使用更小的初始化
                    nn.init.xavier_uniform_(param, gain=0.1)
                elif 'multimodal_encoder' in name:
                    # 多模态编码器使用小初始化
                    nn.init.xavier_uniform_(param, gain=0.2)
                elif 'graph_decoder' in name:
                    # 图解码器使用小初始化
                    nn.init.xavier_uniform_(param, gain=0.3)
                elif 'plm' not in name:  # 不修改PLM权重
                    # 其他层使用标准初始化但缩放
                    nn.init.xavier_uniform_(param, gain=0.5)
            elif 'bias' in name and 'plm' not in name:
                nn.init.zeros_(param)
    
    def _create_stable_optimizer(self):
        """创建稳定的优化器"""
        # 分层学习率，特别关注多模态层
        param_groups = []
        
        # PLM层 - 最小学习率
        plm_params = []
        # 多模态投影层 - 极小学习率
        modal_proj_params = []
        # 多模态编码器 - 小学习率
        multimodal_encoder_params = []
        # 解码器 - 中等学习率
        decoder_params = []
        # 其他参数
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'plm' in name:
                plm_params.append(param)
            elif 'modal_projections' in name:
                modal_proj_params.append(param)
            elif 'multimodal_encoder' in name:
                multimodal_encoder_params.append(param)
            elif 'graph_decoder' in name:
                decoder_params.append(param)
            else:
                other_params.append(param)
        
        param_groups = [
            {'params': plm_params, 'lr': 5e-7, 'weight_decay': 0.01},
            {'params': modal_proj_params, 'lr': 1e-7, 'weight_decay': 0.001},  # 最小学习率
            {'params': multimodal_encoder_params, 'lr': 5e-7, 'weight_decay': 0.001},
            {'params': decoder_params, 'lr': 1e-6, 'weight_decay': 0.001},
            {'params': other_params, 'lr': 5e-7, 'weight_decay': 0.001}
        ]
        
        optimizer = torch.optim.AdamW(param_groups, eps=1e-8, betas=(0.9, 0.999))
        
        logger.info("📊 优化器参数组:")
        logger.info(f"  PLM: {len(plm_params)} 参数, lr=5e-7")
        logger.info(f"  多模态投影: {len(modal_proj_params)} 参数, lr=1e-7")
        logger.info(f"  多模态编码器: {len(multimodal_encoder_params)} 参数, lr=5e-7")
        logger.info(f"  解码器: {len(decoder_params)} 参数, lr=1e-6")
        logger.info(f"  其他: {len(other_params)} 参数, lr=5e-7")
        
        return optimizer
    
    def _create_stable_scheduler(self):
        """创建稳定的学习率调度器"""
        # 使用平台调度器，更加保守
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=0.8,
            patience=3,
            verbose=True,
            min_lr=1e-8
        )
        return scheduler
    
    def get_teacher_forcing_ratio(self, epoch):
        """获取teacher forcing比率 - 更保守的衰减"""
        initial_ratio = 0.95
        final_ratio = 0.8  # 保持较高的teacher forcing
        decay_rate = 0.99  # 非常慢的衰减
        
        ratio = final_ratio + (initial_ratio - final_ratio) * (decay_rate ** epoch)
        return max(ratio, final_ratio)
    
    def train_step(self, batch, epoch):
        """稳定的训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            target_seqs = target_seqs.to(device)
            
            if target_seqs.sum() == 0:
                return None, "空目标序列"
            
            teacher_forcing_ratio = self.get_teacher_forcing_ratio(epoch)
            
            # 前向传播
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            
            # 计算损失
            loss = self.criterion(decoder_outputs, target_seqs)
            
            # 检查损失有效性
            if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 50.0:
                return None, f"异常损失: {loss.item()}"
            
            # 梯度累积
            loss = loss / self.accumulation_steps
            loss.backward()
            
            # 每accumulation_steps步更新一次
            if (self.step_count + 1) % self.accumulation_steps == 0:
                # 严格的梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.max_grad_norm
                )
                
                # 检查梯度范数
                if grad_norm > 5.0:
                    logger.warning(f"大梯度范数: {grad_norm:.4f}")
                    # 跳过这次更新
                    self.optimizer.zero_grad()
                    return None, f"梯度过大: {grad_norm:.4f}"
                
                # 更新参数
                self.optimizer.step()
                self.optimizer.zero_grad()
            
            self.step_count += 1
            
            return loss.item() * self.accumulation_steps, None
            
        except Exception as e:
            return None, f"训练步骤错误: {str(e)}"
    
    def should_early_stop(self, current_f1, patience=10):
        """早停判断"""
        if current_f1 > self.best_f1:
            self.best_f1 = current_f1
            self.patience_counter = 0
            return False, True
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience, False


def create_datasets(config):
    """创建数据集"""
    data_path = config.dataset.get_data_path()
    
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        modalities=['text', 'visual', 'audio']
    )
    
    return train_dataset, dev_dataset


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def main():
    """主训练函数"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)
    
    # 稳定训练配置
    config.training.batch_size = 2  # 小批次，减少内存压力
    config.training.epochs = 20
    
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    
    config.print_config()
    print_args(args)
    
    logger.info("🛡️ 开始稳定训练（修复版）...")
    logger.info("🔧 修复策略:")
    logger.info("  - 重新初始化模型权重")
    logger.info("  - 极保守的分层学习率")
    logger.info("  - 严格的梯度裁剪 (0.5)")
    logger.info("  - 增加梯度累积 (8步)")
    logger.info("  - 损失值限制")
    
    set_seed(config.experiment.seed)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset, dev_dataset = create_datasets(config)
    
    # 创建词汇表
    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)
    
    logger.info(f"词汇表大小: {config.model.vocab_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=0  # 避免多进程问题
    )
    
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=0
    )
    
    # 创建模型
    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 创建稳定训练器
    trainer = StableTrainer(model, token2idx, config)
    
    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.3  # 降低目标，更现实
    
    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        failed_batches = 0
        
        teacher_forcing_ratio = trainer.get_teacher_forcing_ratio(epoch)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.3f}")
        
        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            loss, error_msg = trainer.train_step(batch, epoch)
            
            if loss is not None:
                train_loss += loss
                train_batches += 1
            else:
                failed_batches += 1
                if batch_idx % 100 == 0 and error_msg:
                    logger.warning(f"批次{batch_idx}: {error_msg}")
        
        avg_train_loss = train_loss / max(1, train_batches)
        success_rate = train_batches / (train_batches + failed_batches) * 100
        
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")
        logger.info(f"成功率: {success_rate:.1f}% ({train_batches}/{train_batches + failed_batches})")
        
        # 每2个epoch验证一次
        if (epoch + 1) % 2 == 0:
            model.eval()
            val_loss = 0.0
            val_batches = 0
            all_predictions = []
            all_targets = []
            
            with torch.no_grad():
                for batch in tqdm(dev_loader, desc="验证"):
                    try:
                        target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                        target_seqs = target_seqs.to(device)
                        
                        decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                        loss = trainer.criterion(decoder_outputs, target_seqs)
                        val_loss += loss.item()
                        val_batches += 1
                        
                        predictions = torch.argmax(decoder_outputs, dim=-1)
                        all_predictions.append(predictions)
                        all_targets.append(target_seqs)
                        
                    except Exception as e:
                        continue
            
            avg_val_loss = val_loss / max(1, val_batches)
            
            # 计算指标
            if all_predictions and all_targets:
                combined_predictions = torch.cat(all_predictions, dim=0)
                combined_targets = torch.cat(all_targets, dim=0)
                
                post_processor = UnifiedPostProcessor(mode='optimal')
                metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
            else:
                metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
            
            logger.info(f"验证损失: {avg_val_loss:.4f}")
            logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
            
            # 学习率调度
            trainer.scheduler.step(metrics['f1'])
            
            # 早停检查
            should_stop, is_best = trainer.should_early_stop(metrics['f1'])
            
            if is_best:
                best_f1 = metrics['f1']
                best_metrics = metrics.copy()
                logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")
                
                # 保存模型
                output_dir = Path(config.experiment.output_dir)
                output_dir.mkdir(exist_ok=True)
                model_save_path = output_dir / f"best_stable_fixed_model_{config.dataset.name}.pt"
                torch.save(model.state_dict(), model_save_path)
                logger.info(f"  模型已保存到: {model_save_path}")
                
                if best_f1 >= target_f1:
                    logger.info(f"🎉 达到目标F1分数 {target_f1}！")
            
            if should_stop:
                logger.info("早停触发")
                break
    
    logger.info(f"\n🛡️ 稳定训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    
    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    main()
