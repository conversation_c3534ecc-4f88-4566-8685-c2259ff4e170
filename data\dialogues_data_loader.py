#!/usr/bin/env python3
"""
对话数据加载器
提供基本的数据加载功能
"""

import json
import os
from typing import Dict, List, Any
from utils.UtteranceItem import UtteranceItem
from utils.EmotionCauseLink import EmotionCauseLink


def load_dialogues_json_data(dataset_type: str, data_path: str = None) -> Dict[str, UtteranceItem]:
    """
    加载对话JSON数据
    
    Args:
        dataset_type: 数据集类型 ('train', 'dev', 'test')
        data_path: 数据路径
    
    Returns:
        utterances: 话语字典
    """
    # 这是一个简化的实现，主要用于兼容性
    # 实际项目中主要使用多模态数据集
    
    utterances = {}
    
    # 如果没有提供数据路径，返回空字典
    if data_path is None:
        return utterances
    
    # 构建文件路径
    json_file = os.path.join(data_path, f"{dataset_type}.json")
    
    # 如果文件不存在，返回空字典
    if not os.path.exists(json_file):
        print(f"警告: 数据文件不存在: {json_file}")
        return utterances
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 解析数据
        for dialog_id, dialog_data in data.items():
            if isinstance(dialog_data, list):
                for i, utt_data in enumerate(dialog_data):
                    utt_id = f"{dialog_id}_{i}"
                    
                    # 创建UtteranceItem
                    utterance = UtteranceItem(
                        utterance_id=utt_id,
                        text=utt_data.get('text', ''),
                        speaker=utt_data.get('speaker', 'unknown'),
                        emotion=utt_data.get('emotion', 'neutral'),
                        emotion_cause_links=[]
                    )
                    
                    # 添加情感原因链接
                    if 'emotion_cause_pairs' in utt_data:
                        for pair in utt_data['emotion_cause_pairs']:
                            if isinstance(pair, dict):
                                link = EmotionCauseLink(
                                    emotion_utterance_id=pair.get('emotion_utt', utt_id),
                                    cause_utterance_id=pair.get('cause_utt', utt_id),
                                    emotion=pair.get('emotion', 'neutral')
                                )
                                utterance.emotion_cause_links.append(link)
                    
                    utterances[utt_id] = utterance
    
    except Exception as e:
        print(f"加载数据文件时出错: {e}")
        return {}
    
    return utterances


def load_meld_data(data_path: str, split: str = 'train') -> Dict[str, UtteranceItem]:
    """
    加载MELD数据集
    
    Args:
        data_path: 数据路径
        split: 数据分割 ('train', 'dev', 'test')
    
    Returns:
        utterances: 话语字典
    """
    return load_dialogues_json_data(split, data_path)


def load_iemocap_data(data_path: str, split: str = 'train') -> Dict[str, UtteranceItem]:
    """
    加载IEMOCAP数据集
    
    Args:
        data_path: 数据路径
        split: 数据分割 ('train', 'test')
    
    Returns:
        utterances: 话语字典
    """
    return load_dialogues_json_data(split, data_path)


# 兼容性函数
def load_data(dataset_type: str, data_path: str = None) -> Dict[str, UtteranceItem]:
    """
    通用数据加载函数
    """
    return load_dialogues_json_data(dataset_type, data_path)
