"""
Data module for Graph2Seq ECPE project.

This module contains dataset loading and preprocessing utilities.
"""

# 只导入多模态数据集，避免导入问题
from .multimodal_dataset import MultimodalDialogDataset, multimodal_collate_fn
from .edge_types import *

# 尝试导入单模态数据集，如果失败则跳过
try:
    from .dataset import DialogDataset, collate_fn
    _has_dialog_dataset = True
except ImportError:
    _has_dialog_dataset = False
    DialogDataset = None
    collate_fn = None

__all__ = [
    'MultimodalDialogDataset',
    'multimodal_collate_fn'
]

# 只有在成功导入时才添加到__all__
if _has_dialog_dataset:
    __all__.extend(['DialogDataset', 'collate_fn'])