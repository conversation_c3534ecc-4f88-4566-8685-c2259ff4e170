"""
多模态Graph2Seq模型用于情感原因对提取
扩展原有的Graph2SeqECPE模型以支持多模态输入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoConfig
from typing import List, Tuple, Optional

from .graph2seq_ecpe import Graph2SeqECPE
from .multimodal_graph_encoder import MultimodalGraphEncoder
from .graph_decoder import Graph2SeqDecoder
from .enhanced_multimodal_graph import EnhancedMultimodalGraphBuilder, MultimodalEdgeTypeEncoder
from .enhanced_multimodal_gnn import EnhancedMultimodalGNN
from utils.graphify import create_utterance_features


class MultimodalGraph2SeqECPE(nn.Module):
    """
    多模态Graph2Seq模型用于情感原因对提取
    
    结合MMGCN的多模态处理策略和Graph2ECPE的序列生成架构
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 加载预训练语言模型
        PLM_path = "/root/" + config.plm_model
        self.plm_config = AutoConfig.from_pretrained(PLM_path, local_files_only=True)
        self.plm = AutoModel.from_pretrained(PLM_path, local_files_only=True)
        
        # 如果指定，冻结预训练模型参数
        if getattr(config, 'freeze_plm', False):
            for param in self.plm.parameters():
                param.requires_grad = False
        
        # 话语特征投影层
        self.utt_proj = nn.Linear(self.plm_config.hidden_size, config.hidden_size)
        
        # 多模态配置
        self.modalities = getattr(config, 'modalities', ['text', 'visual', 'audio'])
        self.visual_dim = getattr(config, 'visual_dim', 4096)
        self.audio_dim = getattr(config, 'audio_dim', 6373)
        self.use_multimodal = getattr(config, 'use_multimodal', True)

        # 修复：在初始化时创建所有投影层，避免动态创建
        if self.use_multimodal:
            # 创建模态投影层字典
            self.modal_projections = nn.ModuleDict()

            if 'visual' in self.modalities:
                self.modal_projections['visual'] = nn.Linear(self.visual_dim, config.hidden_size)

            if 'audio' in self.modalities:
                self.modal_projections['audio'] = nn.Linear(self.audio_dim, config.hidden_size)
        
        # 增强的多模态图构建器
        self.enhanced_graph_builder = EnhancedMultimodalGraphBuilder(
            connection_strategy=getattr(config, 'connection_strategy', 'enhanced_cosine'),
            cross_modal_strategy=getattr(config, 'cross_modal_strategy', 'speaker_aware'),
            window_size=getattr(config, 'graph_window_size', 10),
            similarity_threshold=getattr(config, 'similarity_threshold', 0.1),
            normalize_adj=getattr(config, 'normalize_adj', True)
        )

        self.edge_type_encoder = MultimodalEdgeTypeEncoder(
            n_modals=len(self.modalities)
        )

        # 多模态图编码器
        if self.use_multimodal:
            # 获取说话者数量，确保至少为1
            speakers_list = getattr(config, 'speakers', ['_NONE'])
            if not speakers_list or len(speakers_list) == 0:
                speakers_list = ['_NONE']
            n_speakers = max(len(speakers_list), 10)  # 至少10个说话者嵌入

            # 使用增强的多模态GNN
            self.multimodal_encoder = EnhancedMultimodalGNN(
                input_dim=config.hidden_size,
                hidden_dim=config.hidden_size,
                output_dim=config.hidden_size,
                num_layers=getattr(config, 'enhanced_gnn_layers', 3),
                num_edge_types=getattr(config, 'num_edge_types', 5),
                num_modalities=len(self.modalities),
                dropout=config.dropout,
                use_residual=getattr(config, 'use_residual', True),
                use_layer_norm=getattr(config, 'use_layer_norm', True)
            )
        else:
            # 使用原始的单模态编码器
            from .graph_encoders import GNN, EnhancedGATEncoder
            encoder_type = getattr(config, 'graph_encoder_type', 'gnn')
            
            if encoder_type == 'gat':
                self.graph_encoder = EnhancedGATEncoder(
                    input_size=config.hidden_size,
                    hidden_size=config.hidden_size,
                    num_layers=getattr(config, 'gat_layers', 2),
                    num_heads=getattr(config, 'gat_heads', 8),
                    num_edge_types=getattr(config, 'num_edge_types', 13),
                    dropout=config.dropout,
                    pooling_method=getattr(config, 'pooling_method', 'attention')
                )
            else:
                self.graph_encoder = GNN(
                    in_channels=config.hidden_size,
                    hidden_channels=config.hidden_size,
                    out_channels=config.hidden_size,
                    num_layers=config.gnn_layers,
                    dropout=config.dropout,
                    num_edge_types=getattr(config, 'num_edge_types', 5),
                    pooling_method=getattr(config, 'pooling_method', 'attention')
                )
        
        # 图解码器
        # 确保词汇表大小至少为1
        vocab_size = getattr(config, 'vocab_size', 0)
        if vocab_size <= 0:
            vocab_size = 1000  # 默认词汇表大小

        self.graph_decoder = Graph2SeqDecoder(
            hidden_size=config.hidden_size,
            vocab_size=vocab_size,
            num_layers=config.decoder_layers,
            dropout=config.dropout,
            max_length=config.max_target_length
        )
    
    def forward(self, batch, target_seq=None, teacher_forcing_ratio=0.5):
        """
        前向传播
        
        Args:
            batch: 批次数据，包含多模态特征
            target_seq: 目标序列
            teacher_forcing_ratio: 教师强制比例
            
        Returns:
            decoder_outputs: 解码器输出
            attention_weights: 注意力权重
        """
        device = next(self.parameters()).device
        
        # 解析批次数据
        if len(batch) >= 12:  # 多模态数据格式
            (dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, 
             utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch, 
             emotion_cause_edge_indices_batch, visual_features_batch, audio_features_batch) = batch
        else:
            # 兼容原始单模态格式
            (dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, 
             utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch) = batch[:9]
            emotion_cause_edge_indices_batch = batch[9] if len(batch) > 9 else [[] for _ in range(len(edge_index_batch))]
            visual_features_batch = None
            audio_features_batch = None
        
        # 将数据移至设备
        dialog_tokens = dialog_tokens.to(device)
        dialog_uttid = dialog_uttid.to(device)
        dialog_mask = dialog_mask.to(device)
        utt_mask = utt_mask.to(device)
        utt_speakers = utt_speakers.to(device)
        utt_emotions = utt_emotions.to(device)
        
        # 通过预训练模型获取token嵌入
        with torch.set_grad_enabled(not getattr(self.config, 'freeze_plm', False)):
            plm_outputs = self.plm(
                input_ids=dialog_tokens,
                attention_mask=dialog_mask,
                return_dict=True
            )
            token_embs = plm_outputs.last_hidden_state
        
        # 创建话语级特征
        utterance_features, lengths = create_utterance_features(
            token_embs, dialog_uttid, dialog_mask, self.config.hidden_size, device
        )
        
        # 投影话语特征
        projected_features = self.utt_proj(utterance_features)
        
        if self.use_multimodal and visual_features_batch is not None and audio_features_batch is not None:
            # 使用多模态编码
            # 移动多模态特征到设备
            visual_features_device = []
            audio_features_device = []

            for vf, af in zip(visual_features_batch, audio_features_batch):
                visual_features_device.append(vf.to(device))
                audio_features_device.append(af.to(device))

            # 使用增强的多模态图构建器
            node_embeddings, graph_embedding = self._enhanced_multimodal_encoding(
                projected_features, visual_features_device, audio_features_device,
                utt_speakers, lengths, device
            )
        else:
            # 使用原始单模态编码
            from utils.graphify import batch_graphify
            node_features, edge_index, edge_norm, edge_type, batch_indices = batch_graphify(
                projected_features, lengths, utt_speakers, 
                edge_index_batch, edge_type_batch, edge_norm_batch, device
            )
            
            # 图编码
            node_embeddings, graph_embedding = self.graph_encoder(
                node_features, edge_index, edge_type, edge_norm, batch_indices
            )
        
        # 准备情感-原因节点信息（为增强图结构创建batch索引）
        batch_indices = []
        for batch_idx, length in enumerate(lengths):
            batch_indices.extend([batch_idx] * length)
        batch_indices = torch.tensor(batch_indices, dtype=torch.long, device=device)

        emotion_cause_nodes = self._prepare_emotion_cause_nodes(
            emotion_cause_edge_indices_batch, edge_index_batch, batch_indices, device
        )
        
        # 解码生成情感-原因对
        decoder_outputs, attention_weights = self.graph_decoder(
            node_embeddings, graph_embedding, target_seq, batch_indices, 
            teacher_forcing_ratio, emotion_cause_nodes
        )
        
        return decoder_outputs, attention_weights

    def _enhanced_multimodal_encoding(self,
                                     text_features: torch.Tensor,
                                     visual_features: List[torch.Tensor],
                                     audio_features: List[torch.Tensor],
                                     utt_speakers: torch.Tensor,
                                     lengths: List[int],
                                     device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用增强的多模态图构建器进行编码

        Args:
            text_features: 文本特征 [total_utterances, hidden_dim]
            visual_features: 视觉特征列表
            audio_features: 音频特征列表
            utt_speakers: 说话者ID [total_utterances]
            lengths: 每个对话的话语数量
            device: 设备

        Returns:
            node_embeddings: 节点嵌入
            graph_embedding: 图嵌入
        """
        # 1. 准备多模态特征列表
        features_list = []
        modal_ids = []
        total_length = sum(lengths)

        # 文本特征
        features_list.append(text_features)
        modal_ids.extend([0] * total_length)  # 文本模态ID = 0

        # 视觉特征 - 修复维度不匹配问题
        visual_features_2d = []
        for vf in visual_features:
            # 确保特征是2D的
            if vf.dim() == 1:
                vf = vf.unsqueeze(0)  # [1, feature_dim]
            elif vf.dim() > 2:
                vf = vf.view(vf.size(0), -1)  # 展平除第一维外的所有维度
            
            # 检查并调整特征维度
            if vf.size(-1) != self.visual_dim:
                # 如果维度不匹配，使用线性变换或截断/填充
                if vf.size(-1) > self.visual_dim:
                    vf = vf[:, :self.visual_dim]  # 截断
                else:
                    # 填充零
                    padding = torch.zeros(vf.size(0), self.visual_dim - vf.size(-1), device=device)
                    vf = torch.cat([vf, padding], dim=-1)
            
            visual_features_2d.append(vf)
        
        visual_concat = torch.cat(visual_features_2d, dim=0)  # [total_utterances, visual_dim]
        visual_projected = self.modal_projections['visual'](visual_concat)
        features_list.append(visual_projected)
        modal_ids.extend([1] * total_length)  # 视觉模态ID = 1

        # 音频特征 - 修复维度不匹配问题
        audio_features_2d = []
        for af in audio_features:
            # 确保特征是2D的
            if af.dim() == 1:
                af = af.unsqueeze(0)  # [1, feature_dim]
            elif af.dim() > 2:
                af = af.view(af.size(0), -1)  # 展平除第一维外的所有维度
            
            # 检查并调整特征维度
            if af.size(-1) != self.audio_dim:
                # 如果维度不匹配，使用线性变换或截断/填充
                if af.size(-1) > self.audio_dim:
                    af = af[:, :self.audio_dim]  # 截断
                else:
                    # 填充零
                    padding = torch.zeros(af.size(0), self.audio_dim - af.size(-1), device=device)
                    af = torch.cat([af, padding], dim=-1)
            
            audio_features_2d.append(af)
        
        audio_concat = torch.cat(audio_features_2d, dim=0)  # [total_utterances, audio_dim]
        audio_projected = self.modal_projections['audio'](audio_concat)
        features_list.append(audio_projected)
        modal_ids.extend([2] * total_length)  # 音频模态ID = 2

        # 5. 准备节点特征和模态ID - 修复维度不匹配问题
        # 确保所有特征具有相同的维度
        aligned_features = []
        target_dim = features_list[0].size(-1)  # 使用第一个特征的维度作为目标
        
        for feat in features_list:
            if feat.size(-1) != target_dim:
                # 如果维度不匹配，进行调整
                if feat.size(-1) > target_dim:
                    # 截断
                    feat = feat[:, :target_dim]
                else:
                    # 填充
                    padding = torch.zeros(feat.size(0), target_dim - feat.size(-1), device=device)
                    feat = torch.cat([feat, padding], dim=-1)
            aligned_features.append(feat)

        # 2. 使用简化的图构建策略 - 避免复杂的图构建问题
        try:
            # 尝试使用增强的图构建器
            adj_matrix = self.enhanced_graph_builder.create_multimodal_adjacency(
                features_list=aligned_features,
                utterance_lengths=lengths,
                speaker_ids=utt_speakers,
                modalities=self.modalities
            )

            # 从邻接矩阵创建边索引和边权重
            edge_index, edge_weights = self.enhanced_graph_builder.create_edge_index_from_adjacency(adj_matrix)

            # 编码边类型
            edge_types = self.edge_type_encoder.encode_edge_types(
                adj_matrix=adj_matrix,
                features_list=aligned_features,
                utterance_lengths=lengths,
                speaker_ids=utt_speakers
            )
        except Exception as e:
            # 如果图构建失败，使用最简单的连接策略
            print(f"图构建失败，使用简单连接: {e}")
            total_nodes = len(modal_ids)
            
            # 创建最简单的自连接图
            edge_index = torch.arange(total_nodes, device=device).unsqueeze(0).repeat(2, 1)
            edge_weights = torch.ones(total_nodes, device=device)
            edge_types = torch.zeros(total_nodes, dtype=torch.long, device=device)
        
        all_features = torch.cat(aligned_features, dim=0)  # [3 * total_utterances, hidden_dim]
        modal_ids_tensor = torch.tensor(modal_ids, device=device, dtype=torch.long)

        # 6. 使用增强的多模态GNN进行编码
        node_embeddings = self.multimodal_encoder(
            x=all_features,
            edge_index=edge_index,
            edge_type=edge_types,
            modal_ids=modal_ids_tensor,
            utterance_lengths=lengths
        )

        # 7. 提取文本模态的节点嵌入（用于后续解码）
        text_node_embeddings = node_embeddings[:total_length]  # 只取文本模态的嵌入

        # 8. 计算图级嵌入（简单平均池化）
        graph_embeddings = []
        start_idx = 0
        for length in lengths:
            end_idx = start_idx + length
            dialog_embedding = text_node_embeddings[start_idx:end_idx].mean(dim=0)
            graph_embeddings.append(dialog_embedding)
            start_idx = end_idx

        graph_embedding = torch.stack(graph_embeddings, dim=0)  # [batch_size, hidden_dim]

        return text_node_embeddings, graph_embedding

    def _prepare_emotion_cause_nodes(self, emotion_cause_edge_indices_batch,
                                   edge_index_batch, batch_indices, device):
        """准备情感-原因节点信息"""
        emotion_cause_nodes = []

        if emotion_cause_edge_indices_batch:
            # 创建情感-原因节点索引映射
            batch_to_nodes = {}

            # 为每个batch收集节点起始位置
            for b in range(batch_indices.max().item() + 1):
                batch_nodes = (batch_indices == b).nonzero(as_tuple=True)[0]
                if batch_nodes.size(0) > 0:
                    batch_to_nodes[b] = (batch_nodes.min().item(), batch_nodes.max().item())
                else:
                    batch_to_nodes[b] = (0, 0)

            # 为每个batch收集情感-原因节点
            for b in range(len(emotion_cause_edge_indices_batch)):
                ec_nodes = set()
                if b < len(edge_index_batch) and b < len(emotion_cause_edge_indices_batch):
                    # 获取当前batch的边和情感-原因边索引
                    edges = edge_index_batch[b]
                    ec_indices = emotion_cause_edge_indices_batch[b]

                    # 当前batch的节点偏移
                    if b in batch_to_nodes:
                        start_idx, _ = batch_to_nodes[b]

                        # 收集边的节点
                        for idx in ec_indices:
                            if 0 <= idx < len(edges):
                                src, tgt = edges[idx]
                                # 添加全局偏移
                                ec_nodes.add(src + start_idx)

                emotion_cause_nodes.append(list(ec_nodes))

        # 如果情感-原因节点列表为空，填充空列表
        while len(emotion_cause_nodes) < batch_indices.max().item() + 1:
            emotion_cause_nodes.append([])

        return emotion_cause_nodes

    def predict(self, batch, max_length=20):
        """
        预测方法，支持多模态输入
        """
        self.eval()
        device = next(self.parameters()).device

        with torch.no_grad():
            # 创建虚拟目标序列
            sos_token_id = 1
            eos_token_id = 2
            pad_token_id = 0

            # 确定批次大小
            if len(batch) >= 12:
                utt_mask = batch[3]
            else:
                utt_mask = batch[3]
            batch_size = utt_mask.size(0)

            # 创建预测序列
            predictions = torch.full((batch_size, max_length), pad_token_id,dtype=torch.long, device=device)
            predictions[:, 0] = sos_token_id

            # 逐步生成
            for step in range(1, max_length):
                current_seq = predictions[:, :step+1].clone()
                current_seq[:, step] = pad_token_id

                # 前向传播
                decoder_outputs, _ = self.forward(batch, current_seq, teacher_forcing_ratio=0.0)

                # 获取当前步的logits
                current_logits = decoder_outputs[:, step-1, :]

                # 应用温度缩放
                temperature = 0.8
                current_logits = current_logits / temperature

                # 改进的约束解码
                for b in range(batch_size):
                    # 获取当前序列
                    current_seq = predictions[b, :step].tolist()

                    # 惩罚重复的特殊token
                    if step > 1:
                        prev_token = predictions[b, step-1].item()
                        # 强烈惩罚连续的相同token
                        current_logits[b, prev_token] -= 3.0

                        # 如果前一个是<sos>，惩罚再次生成<sos>
                        if prev_token == 1:  # <sos>
                            current_logits[b, 1] -= 10.0

                    # 序列状态约束
                    if len(current_seq) >= 2:
                        # 如果刚生成了话语ID，下一个应该是情感词
                        if any(str(current_seq[-1]).startswith('utt_') for _ in [1]):
                            # 查找话语token的索引范围
                            utt_start = 4  # utt_000开始的索引
                            utt_end = 104  # utt_099结束的索引
                            if utt_start <= current_seq[-1] <= utt_end:
                                # 惩罚话语token，鼓励情感token
                                current_logits[b, utt_start:utt_end] -= 2.0
                                # 鼓励情感token (索引104-111)
                                emotion_start = 104
                                emotion_end = 112
                                current_logits[b, emotion_start:emotion_end] += 1.0

                # 贪婪解码
                next_tokens = torch.argmax(current_logits, dim=-1)
                predictions[:, step] = next_tokens

                # 检查是否遇到EOS
                finished_mask = (next_tokens == eos_token_id)
                if torch.all(finished_mask):
                    break

            return predictions
