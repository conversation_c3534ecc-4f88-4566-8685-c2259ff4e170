#!/usr/bin/env python3
"""
增强的多模态图神经网络
基于MMGCN的深度图卷积网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, MessagePassing
from torch_geometric.utils import add_self_loops, degree
from typing import List, Optional, Tuple
import math


class MultimodalGraphConvLayer(MessagePassing):
    """
    多模态图卷积层
    支持不同类型的边和多模态特征融合
    """
    
    def __init__(self, 
                 in_channels: int,
                 out_channels: int,
                 num_edge_types: int = 5,
                 num_modalities: int = 3,
                 dropout: float = 0.1,
                 activation: str = 'relu'):
        super().__init__(aggr='add')
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_edge_types = num_edge_types
        self.num_modalities = num_modalities
        self.dropout = dropout
        
        # 为每种边类型创建不同的变换矩阵
        self.edge_transforms = nn.ModuleList([
            nn.Linear(in_channels, out_channels) for _ in range(num_edge_types)
        ])
        
        # 模态特定的变换
        self.modal_transforms = nn.ModuleList([
            nn.Linear(in_channels, out_channels) for _ in range(num_modalities)
        ])
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=out_channels,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 门控机制
        self.gate = nn.Sequential(
            nn.Linear(out_channels * 2, out_channels),
            nn.Sigmoid()
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(out_channels)
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.2)
        else:
            self.activation = nn.ReLU()
        
        # Dropout
        self.dropout_layer = nn.Dropout(dropout)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        """重置参数"""
        for transform in self.edge_transforms:
            nn.init.xavier_uniform_(transform.weight)
            nn.init.zeros_(transform.bias)
        
        for transform in self.modal_transforms:
            nn.init.xavier_uniform_(transform.weight)
            nn.init.zeros_(transform.bias)
    
    def forward(self, 
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_type: torch.Tensor,
                modal_ids: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, in_channels]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            modal_ids: 节点的模态ID [num_nodes]
        
        Returns:
            out: 输出特征 [num_nodes, out_channels]
        """
        # 保存残差连接的输入
        residual = x
        
        # 消息传递
        out = self.propagate(edge_index, x=x, edge_type=edge_type)
        
        # 模态特定变换
        if modal_ids is not None:
            modal_out = self._apply_modal_transforms(x, modal_ids)
            out = out + modal_out
        
        # 自注意力
        out_reshaped = out.unsqueeze(0)  # [1, num_nodes, out_channels]
        attn_out, _ = self.attention(out_reshaped, out_reshaped, out_reshaped)
        out = attn_out.squeeze(0)  # [num_nodes, out_channels]
        
        # 门控融合 - 修复维度不匹配问题
        if residual.size(-1) == out.size(-1):
            gate_input = torch.cat([residual, out], dim=-1)
            gate_weights = self.gate(gate_input)
            out = gate_weights * out + (1 - gate_weights) * residual
        else:
            # 如果维度不匹配，使用投影层调整residual维度
            if not hasattr(self, 'residual_projection'):
                self.residual_projection = nn.Linear(residual.size(-1), out.size(-1)).to(residual.device)
            
            projected_residual = self.residual_projection(residual)
            gate_input = torch.cat([projected_residual, out], dim=-1)
            gate_weights = self.gate(gate_input)
            out = gate_weights * out + (1 - gate_weights) * projected_residual
        
        # 层归一化和激活
        out = self.layer_norm(out)
        out = self.activation(out)
        out = self.dropout_layer(out)
        
        return out
    
    def message(self, x_j: torch.Tensor, edge_type: torch.Tensor) -> torch.Tensor:
        """
        消息函数：根据边类型变换邻居特征
        
        Args:
            x_j: 邻居节点特征 [num_edges, in_channels]
            edge_type: 边类型 [num_edges]
        
        Returns:
            messages: 消息 [num_edges, out_channels]
        """
        messages = torch.zeros(x_j.size(0), self.out_channels, device=x_j.device)
        
        # 为每种边类型应用不同的变换
        for edge_t in range(self.num_edge_types):
            mask = (edge_type == edge_t)
            if torch.any(mask):  # 修复：使用torch.any()而不是mask.any()
                messages[mask] = self.edge_transforms[edge_t](x_j[mask])
        
        return messages
    
    def _apply_modal_transforms(self, x: torch.Tensor, modal_ids: torch.Tensor) -> torch.Tensor:
        """应用模态特定的变换"""
        modal_out = torch.zeros(x.size(0), self.out_channels, device=x.device)
        
        for modal_id in range(self.num_modalities):
            mask = (modal_ids == modal_id)
            if torch.any(mask):  # 修复：使用torch.any()而不是mask.any()
                modal_out[mask] = self.modal_transforms[modal_id](x[mask])
        
        return modal_out


class EnhancedMultimodalGNN(nn.Module):
    """
    增强的多模态图神经网络
    多层图卷积 + 跨模态融合 + 注意力机制
    """
    
    def __init__(self,
                 input_dim: int,
                 hidden_dim: int,
                 output_dim: int,
                 num_layers: int = 3,
                 num_edge_types: int = 5,
                 num_modalities: int = 3,
                 dropout: float = 0.1,
                 use_residual: bool = True,
                 use_layer_norm: bool = True):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # 多层图卷积
        self.gnn_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_dim = hidden_dim
            layer_output_dim = hidden_dim if i < num_layers - 1 else output_dim
            
            self.gnn_layers.append(
                MultimodalGraphConvLayer(
                    in_channels=layer_input_dim,
                    out_channels=layer_output_dim,
                    num_edge_types=num_edge_types,
                    num_modalities=num_modalities,
                    dropout=dropout
                )
            )
        
        # 跨模态融合层
        self.cross_modal_fusion = CrossModalFusionLayer(
            hidden_dim=output_dim,
            num_modalities=num_modalities,
            dropout=dropout
        )
        
        # 最终输出层
        self.output_projection = nn.Sequential(
            nn.Linear(output_dim, output_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim, output_dim)
        )
        
        if use_layer_norm:
            self.final_norm = nn.LayerNorm(output_dim)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self,
                x: torch.Tensor,
                edge_index: torch.Tensor,
                edge_type: torch.Tensor,
                modal_ids: Optional[torch.Tensor] = None,
                utterance_lengths: Optional[List[int]] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 [num_nodes, input_dim]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            modal_ids: 节点的模态ID [num_nodes]
            utterance_lengths: 每个对话的话语数量
        
        Returns:
            out: 输出特征 [num_nodes, output_dim]
        """
        # 输入投影
        x = self.input_projection(x)
        x = self.dropout(x)
        
        # 多层图卷积 - 修复维度不匹配问题
        for i, gnn_layer in enumerate(self.gnn_layers):
            x_new = gnn_layer(x, edge_index, edge_type, modal_ids)
            
            # 残差连接
            if self.use_residual and i > 0:
                if x.size(-1) == x_new.size(-1):
                    x = x + x_new
                else:
                    # 维度不匹配时，创建投影层
                    if not hasattr(self, f'layer_projection_{i}'):
                        projection = nn.Linear(x.size(-1), x_new.size(-1)).to(x.device)
                        setattr(self, f'layer_projection_{i}', projection)
                    
                    projection = getattr(self, f'layer_projection_{i}')
                    x_projected = projection(x)
                    x = x_projected + x_new
            else:
                x = x_new
        
        # 跨模态融合
        if modal_ids is not None and utterance_lengths is not None:
            x = self.cross_modal_fusion(x, modal_ids, utterance_lengths)
        
        # 最终输出
        x = self.output_projection(x)
        
        if self.use_layer_norm:
            x = self.final_norm(x)
        
        return x


class CrossModalFusionLayer(nn.Module):
    """
    跨模态融合层
    融合不同模态的信息
    """
    
    def __init__(self, hidden_dim: int, num_modalities: int = 3, dropout: float = 0.1):
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.num_modalities = num_modalities
        
        # 模态间注意力
        self.inter_modal_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 模态融合权重
        self.modal_weights = nn.Parameter(torch.ones(num_modalities))
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim * num_modalities, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.layer_norm = nn.LayerNorm(hidden_dim)
    
    def forward(self, 
                x: torch.Tensor,
                modal_ids: torch.Tensor,
                utterance_lengths: List[int]) -> torch.Tensor:
        """
        跨模态融合
        
        Args:
            x: 输入特征 [num_nodes, hidden_dim]
            modal_ids: 节点的模态ID [num_nodes]
            utterance_lengths: 每个对话的话语数量
        
        Returns:
            fused_features: 融合后的特征 [num_nodes, hidden_dim]
        """
        device = x.device
        total_length = sum(utterance_lengths)
        
        # 分离不同模态的特征 - 修复维度问题
        modal_features = []
        max_modal_length = 0

        for modal_id in range(self.num_modalities):
            mask = (modal_ids == modal_id)
            if torch.any(mask):  # 修复Boolean张量问题
                modal_feat = x[mask]  # [utterances_in_modal, hidden_dim]
                modal_features.append(modal_feat)
                max_modal_length = max(max_modal_length, modal_feat.size(0))
            else:
                # 如果某个模态没有节点，创建零特征
                modal_features.append(torch.zeros(0, self.hidden_dim, device=device))

        # 如果所有模态都为空，直接返回原始特征
        if max_modal_length == 0:
            return x

        # 将所有模态特征填充到相同长度
        padded_features = []
        for modal_feat in modal_features:
            if modal_feat.size(0) == 0:
                # 创建零填充
                padded_feat = torch.zeros(max_modal_length, self.hidden_dim, device=device)
            elif modal_feat.size(0) < max_modal_length:
                # 重复最后一个特征来填充
                padding_size = max_modal_length - modal_feat.size(0)
                last_feat = modal_feat[-1:].repeat(padding_size, 1)
                padded_feat = torch.cat([modal_feat, last_feat], dim=0)
            else:
                # 截断到最大长度
                padded_feat = modal_feat[:max_modal_length]

            padded_features.append(padded_feat)

        modal_features = padded_features
        min_length = max_modal_length
        
        # 堆叠模态特征
        stacked_features = torch.stack(modal_features, dim=0)  # [num_modalities, min_length, hidden_dim]
        
        # 跨模态注意力
        attn_out, _ = self.inter_modal_attention(
            stacked_features, stacked_features, stacked_features
        )  # [num_modalities, min_length, hidden_dim]
        
        # 加权融合
        weights = F.softmax(self.modal_weights, dim=0)
        weighted_features = attn_out * weights.view(-1, 1, 1)
        
        # 拼接并融合
        concatenated = weighted_features.permute(1, 0, 2).contiguous()  # [min_length, num_modalities, hidden_dim]
        concatenated = concatenated.view(min_length, -1)  # [min_length, num_modalities * hidden_dim]
        
        fused = self.fusion_layer(concatenated)  # [min_length, hidden_dim]
        fused = self.layer_norm(fused)
        
        # 将融合结果分配回原始节点
        output = x.clone()  # 从原始特征开始

        for modal_id in range(self.num_modalities):
            mask = (modal_ids == modal_id)
            if torch.any(mask):  # 修复Boolean张量问题
                modal_indices = torch.where(mask)[0]
                modal_length = len(modal_indices)

                # 取融合特征的对应部分
                fusion_length = min(modal_length, min_length)
                if fusion_length > 0:
                    output[modal_indices[:fusion_length]] = fused[:fusion_length]

        return output
