#!/usr/bin/env python3
"""
多模态Graph2ECPE稳定F1优化训练脚本
基于之前的成功经验，稳步提升F1分数到0.6
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
import os
from pathlib import Path

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimplifiedDecoder:
    """
    简化的解码器
    专注于稳定和高效的序列生成
    """

    def __init__(self, model, token2idx, idx2token, max_length=25):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.max_length = max_length

        # 特殊token
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)

        # 有效token集合
        self.emotion_tokens = {'surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear', '_NONE'}
        self.emotion_ids = [token2idx[em] for em in self.emotion_tokens if em in token2idx]
        self.utt_ids = [token2idx[f'utt_{i:03d}'] for i in range(100) if f'utt_{i:03d}' in token2idx]

        self.post_processor = UnifiedPostProcessor(mode='optimal')
    
    def decode(self, batch):
        """简化的解码方法：使用贪婪解码"""
        self.model.eval()
        device = next(self.model.parameters()).device

        with torch.no_grad():
            # 获取批次信息
            if len(batch) >= 12:
                utt_mask = batch[3]
            else:
                utt_mask = batch[3]

            batch_size = utt_mask.size(0)

            # 使用模型的predict方法进行贪婪解码
            predictions = self.model.predict(batch, max_length=self.max_length)

            # 如果predict方法返回空结果，使用备用方法
            if predictions is None or predictions.size(0) == 0:
                predictions = self._fallback_decode(batch, batch_size, device)

            return predictions

    def _fallback_decode(self, batch, batch_size, device):
        """备用解码方法"""
        try:
            # 创建虚拟目标序列进行前向传播
            dummy_targets = torch.full(
                (batch_size, self.max_length),
                self.pad_id,
                device=device,
                dtype=torch.long
            )
            dummy_targets[:, 0] = self.sos_id  # 设置起始token

            # 前向传播
            decoder_outputs, _ = self.model(batch, dummy_targets, teacher_forcing_ratio=0.0)

            # 贪婪解码
            predictions = torch.argmax(decoder_outputs, dim=-1)

            return predictions

        except Exception as e:
            logger.warning(f"备用解码失败: {e}")
            # 返回最基本的预测
            return torch.full(
                (batch_size, self.max_length),
                self.pad_id,
                device=device,
                dtype=torch.long
            )


    def _create_single_batch(self, batch, batch_idx):
        """创建单样本批次"""
        single_batch = []
        for item in batch:
            if isinstance(item, torch.Tensor):
                single_batch.append(item[batch_idx:batch_idx+1])
            elif isinstance(item, list):
                single_batch.append([item[batch_idx]] if batch_idx < len(item) else [[]])
            else:
                single_batch.append(item)
        return single_batch


class SimplifiedECPELoss(nn.Module):
    """
    简化的ECPE损失函数
    专注于核心目标：准确生成情感-原因对序列
    """

    def __init__(self, token2idx, label_smoothing=0.1, structure_weight=0.2):
        super().__init__()
        self.token2idx = token2idx
        self.pad_idx = token2idx.get('<pad>', 0)
        self.sos_idx = token2idx.get('<sos>', 1)
        self.eos_idx = token2idx.get('<eos>', 2)
        self.sep_idx = token2idx.get('<sep>', 3)

        # 使用标签平滑的交叉熵损失
        self.base_criterion = nn.CrossEntropyLoss(
            ignore_index=self.pad_idx,
            label_smoothing=label_smoothing
        )

        # 结构化损失权重
        self.structure_weight = structure_weight

        # 重要token的权重
        self.important_tokens = {self.sep_idx, self.eos_idx}

    def forward(self, logits, targets):
        """
        计算损失

        Args:
            logits: [batch_size, seq_len, vocab_size]
            targets: [batch_size, seq_len]
        """
        batch_size, seq_len, vocab_size = logits.shape

        # 1. 基础交叉熵损失
        base_loss = self.base_criterion(
            logits.view(-1, vocab_size),
            targets.view(-1)
        )

        # 2. 结构化损失：对重要token加权
        structure_loss = self._compute_structure_loss(logits, targets)

        # 3. 组合损失
        total_loss = base_loss + self.structure_weight * structure_loss

        return total_loss

    def _compute_structure_loss(self, logits, targets):
        """计算结构化损失，对重要token（如<sep>、<eos>）加权"""
        structure_loss = 0.0
        count = 0

        for token_idx in self.important_tokens:
            # 找到目标中包含该token的位置
            token_mask = (targets == token_idx)
            if token_mask.any():
                # 提取对应位置的logits和targets
                token_positions = torch.nonzero(token_mask, as_tuple=False)
                for pos in token_positions:
                    b, t = pos[0], pos[1]
                    token_logits = logits[b, t].unsqueeze(0)  # [1, vocab_size]
                    token_target = targets[b, t].unsqueeze(0)  # [1]

                    # 计算该位置的损失
                    token_loss = nn.CrossEntropyLoss()(token_logits, token_target)
                    structure_loss += token_loss
                    count += 1

        # 平均化
        if count > 0:
            structure_loss = structure_loss / count

        return structure_loss


def simplified_ecpe_loss(logits, targets, token2idx, label_smoothing=0.1):
    """
    简化的ECPE损失函数（函数式接口）
    """
    criterion = SimplifiedECPELoss(token2idx, label_smoothing)
    return criterion(logits, targets)


class StableTrainingStrategy:
    """
    稳定的训练策略
    专注于训练稳定性和收敛性
    """

    def __init__(self, config, model, token2idx):
        self.config = config
        self.model = model
        self.token2idx = token2idx

        # 损失函数
        self.criterion = SimplifiedECPELoss(
            token2idx=token2idx,
            label_smoothing=0.1,
            structure_weight=0.2
        )

        # 优化器设置
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()

        # 训练状态
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.step_count = 0

        # 稳定性参数
        self.max_grad_norm = 1.0
        self.warmup_steps = 100
        self.accumulation_steps = 2  # 梯度累积步数

    def _create_optimizer(self):
        """创建优化器，使用分层学习率"""
        # PLM参数使用较小的学习率
        plm_params = []
        other_params = []

        for name, param in self.model.named_parameters():
            if 'plm' in name:
                plm_params.append(param)
            else:
                other_params.append(param)

        optimizer = torch.optim.AdamW([
            {'params': plm_params, 'lr': self.config.training.learning_rate * 0.1},
            {'params': other_params, 'lr': self.config.training.learning_rate}
        ],
        weight_decay=self.config.training.weight_decay,
        eps=1e-8,
        betas=(0.9, 0.999)
        )

        return optimizer

    def _create_scheduler(self):
        """创建学习率调度器"""
        # 使用余弦退火调度器，更加平滑
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=self.config.training.epochs // 4,  # 重启周期
            T_mult=2,
            eta_min=self.config.training.learning_rate * 0.01
        )
        return scheduler

    def get_teacher_forcing_ratio(self, epoch):
        """获取teacher forcing比率，使用更平滑的衰减"""
        # 使用指数衰减，更加平滑
        initial_ratio = 0.9
        final_ratio = 0.3
        decay_rate = 0.95

        ratio = final_ratio + (initial_ratio - final_ratio) * (decay_rate ** epoch)
        return max(ratio, final_ratio)

    def compute_loss(self, logits, targets):
        """计算损失"""
        return self.criterion(logits, targets)

    def training_step(self, batch, epoch):
        """执行一个训练步骤"""
        try:
            # 准备数据
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            device = next(self.model.parameters()).device
            target_seqs = target_seqs.to(device)

            # 检查目标序列是否有效
            if target_seqs.sum() == 0:
                return None, "Empty target sequence"

            # 获取teacher forcing比率
            teacher_forcing_ratio = self.get_teacher_forcing_ratio(epoch)

            # 前向传播
            decoder_outputs, _ = self.model(
                batch,
                target_seqs,
                teacher_forcing_ratio=teacher_forcing_ratio
            )

            # 计算损失
            loss = self.compute_loss(decoder_outputs, target_seqs)

            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                return None, f"Invalid loss: {loss.item()}"

            # 梯度累积
            loss = loss / self.accumulation_steps
            loss.backward()

            # 每accumulation_steps步更新一次参数
            if (self.step_count + 1) % self.accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.max_grad_norm
                )

                # 更新参数
                self.optimizer.step()
                self.scheduler.step()
                self.optimizer.zero_grad()

            self.step_count += 1

            return loss.item() * self.accumulation_steps, None

        except Exception as e:
            return None, f"Training step error: {str(e)}"

    def should_early_stop(self, current_f1, patience=5):
        """判断是否应该早停"""
        if current_f1 > self.best_f1:
            self.best_f1 = current_f1
            self.patience_counter = 0
            return False, True  # 不早停，但是最佳模型
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience, False


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def create_multimodal_datasets(config):
    """创建多模态数据集"""
    data_path = config.dataset.get_data_path()

    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")

    for file_path in [train_file, dev_file]:
        if not os.path.exists(file_path):
            logger.error(f"数据文件不存在: {file_path}")
            raise FileNotFoundError(f"数据文件不存在: {file_path}")

    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        visual_dim=config.model.visual_dim,
        audio_dim=config.model.audio_dim,
        normalize_features=config.model.normalize_multimodal_features,
        use_speaker_embedding=config.model.use_speaker_embedding,
        use_modal_embedding=config.model.use_modal_embedding
    )

    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        visual_dim=config.model.visual_dim,
        audio_dim=config.model.audio_dim,
        normalize_features=config.model.normalize_multimodal_features,
        use_speaker_embedding=config.model.use_speaker_embedding,
        use_modal_embedding=config.model.use_modal_embedding
    )

    logger.info(f"训练集大小: {len(train_dataset)}")
    logger.info(f"验证集大小: {len(dev_dataset)}")

    return train_dataset, dev_dataset


def train_simplified_stable_model():
    """训练简化稳定模型"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)

    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()

    config.print_config()
    print_args(args)

    logger.info(f"🎯 开始简化稳定训练... (目标F1: 0.6)")

    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    train_dataset, dev_dataset = create_multimodal_datasets(config)

    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)

    logger.info(f"词汇表大小: {config.model.vocab_size}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建简化解码器
    decoder = SimplifiedDecoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        max_length=25
    )

    # 创建稳定训练策略
    training_strategy = StableTrainingStrategy(config, model, token2idx)

    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.6

    logger.info(f"🎯 简化稳定训练策略:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  简化损失函数: 启用")
    logger.info(f"  稳定训练策略: 启用")
    logger.info(f"  梯度累积: 启用")

    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 获取teacher forcing比率
        teacher_forcing_ratio = training_strategy.get_teacher_forcing_ratio(epoch)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.2f}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            # 使用稳定训练策略执行训练步骤
            loss, error_msg = training_strategy.training_step(batch, epoch)

            if loss is not None:
                train_loss += loss
                train_batches += 1
            else:
                if error_msg:
                    logger.warning(f"训练批次 {batch_idx} 出错: {error_msg}")
                continue

        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dev_loader, desc="验证")):
                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                    target_seqs = target_seqs.to(device)

                    decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = training_strategy.compute_loss(decoder_outputs, target_seqs)
                    val_loss += loss.item()
                    val_batches += 1

                    # 使用简化解码
                    predictions = decoder.decode(batch)

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    logger.warning(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        avg_val_loss = val_loss / max(1, val_batches)

        # 计算指标
        if all_predictions and all_targets:
            combined_predictions = torch.cat(all_predictions, dim=0)
            combined_targets = torch.cat(all_targets, dim=0)

            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)

            # 详细分析
            total_matches = 0
            total_pred_pairs = 0
            total_true_pairs = 0

            for i in range(combined_predictions.size(0)):
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]

                pred_pairs = set(post_processor.process(pred_tokens))
                true_pairs = set(post_processor.process(true_tokens))

                matches = len(pred_pairs & true_pairs)
                total_matches += matches
                total_pred_pairs += len(pred_pairs)
                total_true_pairs += len(true_pairs)

            match_rate = total_matches / max(total_true_pairs, 1)

            logger.info(f"匹配分析:")
            logger.info(f"  总匹配对数: {total_matches}")
            logger.info(f"  总预测对数: {total_pred_pairs}")
            logger.info(f"  总真实对数: {total_true_pairs}")
            logger.info(f"  匹配率: {match_rate:.4f}")
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

        logger.info(f"验证损失: {avg_val_loss:.4f}")
        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")

        # 使用训练策略的早停逻辑
        should_stop, is_best = training_strategy.should_early_stop(metrics['f1'], patience=8)

        if is_best:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")

            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")

            # 保存最佳模型
            output_dir = Path(config.experiment.output_dir)
            output_dir.mkdir(exist_ok=True)
            model_save_path = output_dir / f"best_simplified_stable_model_{config.dataset.name}.pt"
            torch.save(model.state_dict(), model_save_path)
            logger.info(f"  模型已保存到: {model_save_path}")
        else:
            logger.info(f"  验证指标未改善 ({training_strategy.patience_counter}/8)")

        if should_stop:
            logger.info(f"早停触发: 连续8次未改善")
            break

        # 显示样例
        if epoch % 3 == 0 and all_predictions:
            logger.info(f"\n预测样例:")
            sample_processor = UnifiedPostProcessor(mode='optimal')
            for i in range(min(2, combined_predictions.size(0))):
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]

                true_pairs = set(sample_processor.process(true_tokens))
                pred_pairs = set(sample_processor.process(pred_tokens))
                matches = true_pairs & pred_pairs

                logger.info(f"  样例 {i+1}:")
                logger.info(f"    真实: {list(true_pairs)}")
                logger.info(f"    预测: {list(pred_pairs)}")
                logger.info(f"    匹配: {list(matches)} ({len(matches)}个)")

    logger.info(f"\n🎯 简化稳定训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")

    if best_f1 >= target_f1:
        logger.info(f"🎉 成功达到目标F1分数 {target_f1}！")
    else:
        gap = target_f1 - best_f1
        improvement_needed = gap / best_f1 * 100 if best_f1 > 0 else 100
        logger.info(f"📈 距离目标还需提升 {improvement_needed:.1f}%")

    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    final_f1, final_precision, final_recall = train_simplified_stable_model()

    logger.info(f"\n🏆 最终结果:")
    logger.info(f"F1分数: {final_f1:.4f}")
    logger.info(f"精确率: {final_precision:.4f}")
    logger.info(f"召回率: {final_recall:.4f}")

    # 与之前结果对比
    baseline_f1 = 0.3056
    if final_f1 > baseline_f1:
        improvement = (final_f1 - baseline_f1) / baseline_f1 * 100
        logger.info(f"📈 相比基线提升: {improvement:.1f}%")

    target_f1 = 0.6
    if final_f1 >= target_f1:
        logger.info(f"🎉 成功达到目标F1分数 {target_f1}！")
    else:
        logger.info(f"📊 继续努力，距离目标F1={target_f1}还有: {target_f1-final_f1:.4f}")
