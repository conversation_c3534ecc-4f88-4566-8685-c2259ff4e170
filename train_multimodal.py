#!/usr/bin/env python3
"""
多模态Graph2ECPE稳定F1优化训练脚本
基于之前的成功经验，稳步提升F1分数到0.6
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
import os
from pathlib import Path

from config import UnifiedConfig, get_config, set_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_loss, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StableF1Decoder:
    """
    稳定F1解码器
    基于召回率优化的成功经验，进一步提升精确匹配率
    """
    
    def __init__(self, model, token2idx, idx2token, max_length=25):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.max_length = max_length
        
        # 特殊token
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)
        
        # 有效token集合
        self.emotion_tokens = {'surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear', '_NONE'}
        self.emotion_ids = [token2idx[em] for em in self.emotion_tokens if em in token2idx]
        self.utt_ids = [token2idx[f'utt_{i:03d}'] for i in range(100) if f'utt_{i:03d}' in token2idx]
        
        self.post_processor = UnifiedPostProcessor(mode='optimal')
    
    def improved_decode_v4(self, batch):
        """改进解码v4：结合召回率优化和精确匹配优化"""
        self.model.eval()
        device = next(self.model.parameters()).device
        
        with torch.no_grad():
            if len(batch) >= 12:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]
            else:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]
            
            batch_size = utt_mask.size(0)
            all_predictions = []
            
            for b in range(batch_size):
                # 获取上下文信息
                valid_utts = utt_mask[b].sum().item()
                sample_emotions = utt_emotions[b][:valid_utts]
                
                # 生成多个候选并选择最佳
                candidates = self._generate_candidates(batch, b, valid_utts, sample_emotions)
                best_candidate = self._select_best_candidate(candidates)
                
                all_predictions.append(best_candidate)
            
            # 填充到相同长度
            if all_predictions:
                max_len = max(len(pred) for pred in all_predictions)
                padded_predictions = []
                for pred in all_predictions:
                    if len(pred) < max_len:
                        pad_length = max_len - len(pred)
                        padded_pred = torch.cat([pred, torch.full((pad_length,), self.pad_id, device=pred.device)])
                    else:
                        padded_pred = pred
                    padded_predictions.append(padded_pred)
                return torch.stack(padded_predictions)
            else:
                return torch.empty((batch_size, 0), device=device, dtype=torch.long)
    
    def _generate_candidates(self, batch, batch_idx, valid_utts, sample_emotions):
        """生成多个候选序列"""
        candidates = []
        
        # 1. 贪婪解码
        greedy_candidate = self._greedy_decode(batch, batch_idx)
        candidates.append(greedy_candidate)
        
        # 2. 温度采样（多个温度）
        temperatures = [0.5, 0.7, 0.9]
        for i, temp in enumerate(temperatures):
            temp_candidate = self._temperature_decode(batch, batch_idx, temp, valid_utts, sample_emotions, seed=i)
            candidates.append(temp_candidate)
        
        # 3. 约束解码
        constrained_candidate = self._constrained_decode(batch, batch_idx, valid_utts, sample_emotions)
        candidates.append(constrained_candidate)
        
        return candidates
    
    def _greedy_decode(self, batch, batch_idx):
        """贪婪解码"""
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        try:
            prediction = self.model.predict(single_batch, max_length=self.max_length)
            return prediction[0]
        except:
            default_seq = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
            default_seq[0] = self.sos_id
            default_seq[1] = self.eos_id
            return default_seq
    
    def _temperature_decode(self, batch, batch_idx, temperature, valid_utts, sample_emotions, seed=0):
        """温度解码"""
        torch.manual_seed(42 + seed)
        
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        sequence = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
        sequence[0] = self.sos_id
        
        # 有效话语和情感
        valid_utt_ids = [self.token2idx.get(f'utt_{i:03d}', self.pad_id) for i in range(valid_utts)]
        
        for step in range(1, self.max_length):
            try:
                current_seq = sequence[:step+1].unsqueeze(0)
                padded_seq = torch.full((1, self.max_length), self.pad_id, device=device, dtype=torch.long)
                padded_seq[0, :current_seq.size(1)] = current_seq[0]
                
                decoder_outputs, _ = self.model(single_batch, padded_seq, teacher_forcing_ratio=0.0)
                
                if step - 1 < decoder_outputs.size(1):
                    logits = decoder_outputs[0, step - 1, :]
                else:
                    logits = decoder_outputs[0, -1, :]
                
                # 应用温度
                logits = logits / temperature
                
                # 轻微的约束（鼓励有效token）
                position_in_pair = (step - 1) % 4
                if position_in_pair == 0 or position_in_pair == 2:  # 话语位置
                    for utt_id in valid_utt_ids:
                        if utt_id != self.pad_id:
                            logits[utt_id] += 0.5
                elif position_in_pair == 1:  # 情感位置
                    for emo_id in self.emotion_ids:
                        logits[emo_id] += 0.3
                elif position_in_pair == 3:  # 分隔符位置
                    logits[self.sep_id] += 1.0
                
                probs = torch.softmax(logits, dim=-1)
                next_token = torch.multinomial(probs, 1).item()
                sequence[step] = next_token
                
                if next_token == self.eos_id:
                    break
                    
            except Exception as e:
                sequence[step] = self.eos_id
                break
        
        return sequence
    
    def _constrained_decode(self, batch, batch_idx, valid_utts, sample_emotions):
        """约束解码"""
        device = next(self.model.parameters()).device
        single_batch = self._create_single_batch(batch, batch_idx)
        
        sequence = torch.full((self.max_length,), self.pad_id, device=device, dtype=torch.long)
        sequence[0] = self.sos_id
        
        valid_utt_ids = [self.token2idx.get(f'utt_{i:03d}', self.pad_id) for i in range(valid_utts)]
        
        step = 1
        pairs_generated = 0
        max_pairs = min(5, valid_utts)
        
        while step < self.max_length and pairs_generated < max_pairs:
            try:
                current_seq = sequence[:step+1].unsqueeze(0)
                padded_seq = torch.full((1, self.max_length), self.pad_id, device=device, dtype=torch.long)
                padded_seq[0, :current_seq.size(1)] = current_seq[0]
                
                decoder_outputs, _ = self.model(single_batch, padded_seq, teacher_forcing_ratio=0.0)
                
                if step - 1 < decoder_outputs.size(1):
                    logits = decoder_outputs[0, step - 1, :]
                else:
                    logits = decoder_outputs[0, -1, :]
                
                # 严格约束
                position_in_pair = (step - 1) % 4
                constraint_mask = torch.full_like(logits, float('-inf'))
                
                if position_in_pair == 0 or position_in_pair == 2:  # 话语位置
                    for utt_id in valid_utt_ids:
                        if utt_id != self.pad_id:
                            constraint_mask[utt_id] = 0
                elif position_in_pair == 1:  # 情感位置
                    for emo_id in self.emotion_ids:
                        constraint_mask[emo_id] = 0
                elif position_in_pair == 3:  # 分隔符位置
                    constraint_mask[self.sep_id] = 0
                    if pairs_generated >= 2:
                        constraint_mask[self.eos_id] = 0
                
                constrained_logits = logits + constraint_mask
                probs = torch.softmax(constrained_logits, dim=-1)
                
                if torch.all(probs == 0):
                    next_token = self.eos_id
                else:
                    next_token = torch.argmax(probs).item()
                
                sequence[step] = next_token
                step += 1
                
                if next_token == self.sep_id:
                    pairs_generated += 1
                elif next_token == self.eos_id:
                    break
                    
            except Exception as e:
                sequence[step] = self.eos_id
                break
        
        return sequence
    
    def _select_best_candidate(self, candidates):
        """选择最佳候选"""
        best_candidate = candidates[0]
        best_score = -1
        
        for candidate in candidates:
            score = self._score_candidate(candidate)
            if score > best_score:
                best_score = score
                best_candidate = candidate
        
        return best_candidate
    
    def _score_candidate(self, candidate):
        """评估候选质量"""
        tokens = [self.idx2token.get(idx.item(), '<unk>') for idx in candidate]
        pairs = self.post_processor.process(tokens)
        
        if len(pairs) == 0:
            return 0.0
        
        score = 0.0
        
        # 基础分数：对的数量
        score += len(pairs) * 2.0
        
        # 质量分数
        for emo_utt, emotion, cause_utt in pairs:
            # 有效性检查
            if emo_utt in {f'utt_{i:03d}' for i in range(100)}:
                score += 1.0
            if emotion in self.emotion_tokens:
                score += 1.5
            if cause_utt in {f'utt_{i:03d}' for i in range(100)}:
                score += 1.0
            
            # 多样性奖励
            if emotion not in ['neutral', '_NONE']:
                score += 0.5
        
        # 结构分数
        if '<eos>' in tokens:
            score += 1.0
        
        return score
    
    def _create_single_batch(self, batch, batch_idx):
        """创建单样本批次"""
        single_batch = []
        for item in batch:
            if isinstance(item, torch.Tensor):
                single_batch.append(item[batch_idx:batch_idx+1])
            elif isinstance(item, list):
                single_batch.append([item[batch_idx]] if batch_idx < len(item) else [[]])
            else:
                single_batch.append(item)
        return single_batch


def stable_f1_loss(logits, targets, token2idx, alpha_recall=0.3, alpha_precision=0.2):
    """稳定F1损失函数"""
    batch_size, seq_len, vocab_size = logits.shape
    pad_idx = token2idx.get('<pad>', 0)
    
    logits_flat = logits.view(-1, vocab_size)
    targets_flat = targets.view(-1)
    
    mask = targets_flat != pad_idx
    if mask.sum() == 0:
        return torch.tensor(0.0, device=logits.device, requires_grad=True)
    
    logits_flat = logits_flat[mask]
    targets_flat = targets_flat[mask]
    
    # 基础交叉熵损失
    base_loss = nn.CrossEntropyLoss()(logits_flat, targets_flat)
    
    # 召回率增强（适度）
    sep_id = token2idx.get('<sep>', 3)
    recall_loss = 0.0
    sep_mask = (targets_flat == sep_id)
    if sep_mask.any():
        sep_logits = logits_flat[sep_mask]
        sep_targets = targets_flat[sep_mask]
        recall_loss = nn.CrossEntropyLoss()(sep_logits, sep_targets) * alpha_recall
    
    # 精确率增强（情感预测）
    emotion_ids = [token2idx[em] for em in ['surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear', '_NONE'] if em in token2idx]
    precision_loss = 0.0
    for emo_id in emotion_ids:
        emo_mask = (targets_flat == emo_id)
        if emo_mask.any():
            emo_logits = logits_flat[emo_mask]
            emo_targets = targets_flat[emo_mask]
            precision_loss += nn.CrossEntropyLoss()(emo_logits, emo_targets) * alpha_precision
    
    total_loss = base_loss + recall_loss + precision_loss
    return total_loss


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def create_multimodal_datasets(config):
    """创建多模态数据集"""
    data_path = config.dataset.get_data_path()

    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")

    for file_path in [train_file, dev_file]:
        if not os.path.exists(file_path):
            logger.error(f"数据文件不存在: {file_path}")
            raise FileNotFoundError(f"数据文件不存在: {file_path}")

    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        visual_dim=config.model.visual_dim,
        audio_dim=config.model.audio_dim,
        normalize_features=config.model.normalize_multimodal_features,
        use_speaker_embedding=config.model.use_speaker_embedding,
        use_modal_embedding=config.model.use_modal_embedding
    )

    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        visual_dim=config.model.visual_dim,
        audio_dim=config.model.audio_dim,
        normalize_features=config.model.normalize_multimodal_features,
        use_speaker_embedding=config.model.use_speaker_embedding,
        use_modal_embedding=config.model.use_modal_embedding
    )

    logger.info(f"训练集大小: {len(train_dataset)}")
    logger.info(f"验证集大小: {len(dev_dataset)}")

    return train_dataset, dev_dataset


def train_stable_f1_model():
    """训练稳定F1模型"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)

    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()

    config.print_config()
    print_args(args)

    logger.info(f"🎯 开始稳定F1优化训练... (目标F1: 0.6)")

    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    train_dataset, dev_dataset = create_multimodal_datasets(config)

    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)

    logger.info(f"词汇表大小: {config.model.vocab_size}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建稳定F1解码器
    f1_decoder = StableF1Decoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        max_length=25
    )

    # 优化器（保守设置）
    optimizer = torch.optim.AdamW([
        {'params': model.plm.parameters(), 'lr': config.training.learning_rate * 0.1},
        {'params': [p for n, p in model.named_parameters() if 'plm' not in n], 'lr': config.training.learning_rate}
    ], weight_decay=config.training.weight_decay)

    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.8, patience=3, verbose=True
    )

    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    patience_counter = 0
    max_patience = 8
    target_f1 = 0.6

    logger.info(f"🎯 稳定F1优化策略:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  多候选解码: 启用")
    logger.info(f"  稳定损失函数: 启用")
    logger.info(f"  保守学习率: 启用")

    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 更激进的teacher forcing降低策略以提高召回率
        teacher_forcing_ratio = max(0.3, 0.8 - epoch * 0.04)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.2f}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            try:
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                target_seqs = target_seqs.to(device)

                if target_seqs.sum() == 0:
                    continue

                optimizer.zero_grad()
                decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)

                # 使用稳定损失函数
                try:
                    loss = stable_f1_loss(decoder_outputs, target_seqs, token2idx,
                                        alpha_recall=0.3, alpha_precision=0.2)

                    if torch.isnan(loss) or loss.item() > 15.0:
                        logger.warning(f"异常损失值 {loss.item():.4f}，跳过此批次")
                        continue
                except Exception as loss_error:
                    logger.warning(f"损失计算出错: {loss_error}，跳过此批次")
                    continue

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()

                train_loss += loss.item()
                train_batches += 1

            except Exception as e:
                logger.warning(f"训练批次 {batch_idx} 出错: {e}")
                continue

        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dev_loader, desc="验证")):
                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                    target_seqs = target_seqs.to(device)

                    decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = stable_f1_loss(decoder_outputs, target_seqs, token2idx)
                    val_loss += loss.item()
                    val_batches += 1

                    # 使用改进解码
                    predictions = f1_decoder.improved_decode_v4(batch)

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    logger.warning(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        avg_val_loss = val_loss / max(1, val_batches)

        # 计算指标
        if all_predictions and all_targets:
            combined_predictions = torch.cat(all_predictions, dim=0)
            combined_targets = torch.cat(all_targets, dim=0)

            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)

            # 详细分析
            total_matches = 0
            total_pred_pairs = 0
            total_true_pairs = 0

            for i in range(combined_predictions.size(0)):
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]

                pred_pairs = set(post_processor.process(pred_tokens))
                true_pairs = set(post_processor.process(true_tokens))

                matches = len(pred_pairs & true_pairs)
                total_matches += matches
                total_pred_pairs += len(pred_pairs)
                total_true_pairs += len(true_pairs)

            match_rate = total_matches / max(total_true_pairs, 1)

            logger.info(f"匹配分析:")
            logger.info(f"  总匹配对数: {total_matches}")
            logger.info(f"  总预测对数: {total_pred_pairs}")
            logger.info(f"  总真实对数: {total_true_pairs}")
            logger.info(f"  匹配率: {match_rate:.4f}")
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

        logger.info(f"验证损失: {avg_val_loss:.4f}")
        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")

        scheduler.step(metrics['f1'])

        # 保存最佳模型
        improved = False
        if metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            improved = True
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")

            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")

        if improved:
            patience_counter = 0
            output_dir = Path(config.experiment.output_dir)
            output_dir.mkdir(exist_ok=True)
            model_save_path = output_dir / f"best_stable_f1_model_{config.dataset.name}.pt"
            torch.save(model.state_dict(), model_save_path)
            logger.info(f"  模型已保存到: {model_save_path}")
        else:
            patience_counter += 1
            logger.info(f"  验证指标未改善 ({patience_counter}/{max_patience})")

        if patience_counter >= max_patience:
            logger.info(f"早停触发: 连续{max_patience}次未改善")
            break

        # 显示样例
        if epoch % 3 == 0 and all_predictions:
            logger.info(f"\n预测样例:")
            sample_processor = UnifiedPostProcessor(mode='optimal')
            for i in range(min(2, combined_predictions.size(0))):
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]

                true_pairs = set(sample_processor.process(true_tokens))
                pred_pairs = set(sample_processor.process(pred_tokens))
                matches = true_pairs & pred_pairs

                logger.info(f"  样例 {i+1}:")
                logger.info(f"    真实: {list(true_pairs)}")
                logger.info(f"    预测: {list(pred_pairs)}")
                logger.info(f"    匹配: {list(matches)} ({len(matches)}个)")

    logger.info(f"\n🎯 稳定F1优化训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")

    if best_f1 >= target_f1:
        logger.info(f"🎉 成功达到目标F1分数 {target_f1}！")
    else:
        gap = target_f1 - best_f1
        improvement_needed = gap / best_f1 * 100
        logger.info(f"📈 距离目标还需提升 {improvement_needed:.1f}%")

    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    final_f1, final_precision, final_recall = train_stable_f1_model()

    logger.info(f"\n🏆 最终结果:")
    logger.info(f"F1分数: {final_f1:.4f}")
    logger.info(f"精确率: {final_precision:.4f}")
    logger.info(f"召回率: {final_recall:.4f}")

    # 与之前结果对比
    baseline_f1 = 0.3056
    if final_f1 > baseline_f1:
        improvement = (final_f1 - baseline_f1) / baseline_f1 * 100
        logger.info(f"📈 相比基线提升: {improvement:.1f}%")

    target_f1 = 0.6
    if final_f1 >= target_f1:
        logger.info(f"🎉 成功达到目标F1分数 {target_f1}！")
    else:
        logger.info(f"📊 继续努力，距离目标F1={target_f1}还有: {target_f1-final_f1:.4f}")
