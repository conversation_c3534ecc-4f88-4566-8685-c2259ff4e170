#!/usr/bin/env python3
"""
改进的训练脚本
基于之前的分析，使用更保守的训练策略
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_multimodal import *

def train_improved_model():
    """改进的训练函数"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)

    # 改进的训练配置
    config.training.learning_rate = 1e-5  # 降低学习率
    config.training.epochs = 25  # 增加训练轮数
    config.training.batch_size = 6  # 稍微减小批次大小

    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()

    config.print_config()
    print_args(args)

    logger.info(f"🎯 开始改进训练... (目标F1: 0.6)")
    logger.info(f"📈 改进策略:")
    logger.info(f"  - 降低学习率: {config.training.learning_rate}")
    logger.info(f"  - 更保守的teacher forcing衰减")
    logger.info(f"  - 基于性能的学习率调度")
    logger.info(f"  - 放宽解码约束")

    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    train_dataset, dev_dataset = create_multimodal_datasets(config)

    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)

    logger.info(f"词汇表大小: {config.model.vocab_size}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建简化解码器
    decoder = SimplifiedDecoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        max_length=25
    )

    # 创建改进的训练策略
    training_strategy = StableTrainingStrategy(config, model, token2idx)

    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.6

    logger.info(f"🎯 改进训练策略:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  学习率: {config.training.learning_rate}")
    logger.info(f"  批次大小: {config.training.batch_size}")
    logger.info(f"  训练轮数: {config.training.epochs}")

    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 获取teacher forcing比率
        teacher_forcing_ratio = training_strategy.get_teacher_forcing_ratio(epoch)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.3f}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            # 使用稳定训练策略执行训练步骤
            loss, error_msg = training_strategy.training_step(batch, epoch)
            
            if loss is not None:
                train_loss += loss
                train_batches += 1
            else:
                if error_msg:
                    logger.warning(f"训练批次 {batch_idx} 出错: {error_msg}")
                continue

        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dev_loader, desc="验证")):
                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                    target_seqs = target_seqs.to(device)

                    decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = training_strategy.compute_loss(decoder_outputs, target_seqs)
                    val_loss += loss.item()
                    val_batches += 1

                    # 使用简化解码
                    predictions = decoder.decode(batch)
                    
                    # 调试信息：只在第一个epoch的第一个批次
                    if batch_idx == 0 and epoch == 0:
                        logger.info(f"调试信息 - Epoch {epoch+1}, 批次 {batch_idx+1}:")
                        logger.info(f"  目标序列样例: {target_seqs[0][:10]}")
                        logger.info(f"  预测序列样例: {predictions[0][:10]}")
                        
                        # 转换为token查看
                        target_tokens = [idx2token.get(idx.item(), f'<{idx.item()}>') for idx in target_seqs[0][:10]]
                        pred_tokens = [idx2token.get(idx.item(), f'<{idx.item()}>') for idx in predictions[0][:10]]
                        logger.info(f"  目标tokens: {target_tokens}")
                        logger.info(f"  预测tokens: {pred_tokens}")

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    logger.warning(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        avg_val_loss = val_loss / max(1, val_batches)

        # 计算指标
        if all_predictions and all_targets:
            combined_predictions = torch.cat(all_predictions, dim=0)
            combined_targets = torch.cat(all_targets, dim=0)
            
            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

        logger.info(f"验证损失: {avg_val_loss:.4f}")
        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")

        # 调用学习率调度器
        training_strategy.scheduler.step(metrics['f1'])
        
        # 使用训练策略的早停逻辑
        should_stop, is_best = training_strategy.should_early_stop(metrics['f1'], patience=10)  # 增加patience
        
        if is_best:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")

            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")

            # 保存最佳模型
            output_dir = Path(config.experiment.output_dir)
            output_dir.mkdir(exist_ok=True)
            model_save_path = output_dir / f"best_improved_model_{config.dataset.name}.pt"
            torch.save(model.state_dict(), model_save_path)
            logger.info(f"  模型已保存到: {model_save_path}")
        else:
            logger.info(f"  验证指标未改善 ({training_strategy.patience_counter}/10)")

        # 每5个epoch显示预测样例
        if (epoch + 1) % 5 == 0:
            logger.info(f"\n预测样例 (Epoch {epoch+1}):")
            show_prediction_examples(combined_predictions, combined_targets, post_processor, idx2token, num_examples=2)

        if should_stop:
            logger.info(f"早停触发: 连续10次未改善")
            break

    logger.info(f"\n🎯 改进训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")

    if best_f1 >= target_f1:
        logger.info(f"🎉 成功达到目标F1分数 {target_f1}！")
    else:
        gap = target_f1 - best_f1
        improvement_needed = gap / best_f1 * 100 if best_f1 > 0 else 100
        logger.info(f"📈 距离目标还需提升 {improvement_needed:.1f}%")

    return best_f1, best_metrics['precision'], best_metrics['recall']


def show_prediction_examples(predictions, targets, post_processor, idx2token, num_examples=2):
    """显示预测样例"""
    for i in range(min(num_examples, predictions.size(0))):
        pred_tokens = [idx2token.get(idx.item(), f'<{idx.item()}>') for idx in predictions[i]]
        target_tokens = [idx2token.get(idx.item(), f'<{idx.item()}>') for idx in targets[i]]
        
        pred_pairs = post_processor.process(pred_tokens)
        target_pairs = post_processor.process(target_tokens)
        
        logger.info(f"  样例 {i+1}:")
        logger.info(f"    真实: {target_pairs}")
        logger.info(f"    预测: {pred_pairs}")


if __name__ == "__main__":
    final_f1, final_precision, final_recall = train_improved_model()
