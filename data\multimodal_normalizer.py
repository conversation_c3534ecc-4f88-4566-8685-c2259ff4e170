"""
多模态特征归一化器
针对不同模态特征的特性设计专门的归一化策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple
from enum import Enum


class NormalizationStrategy(Enum):
    """归一化策略枚举"""
    NONE = "none"                    # 不归一化
    L2_NORM = "l2_norm"             # L2归一化
    BATCH_NORM = "batch_norm"       # 批归一化
    LAYER_NORM = "layer_norm"       # 层归一化
    MIN_MAX = "min_max"             # 最小-最大归一化
    Z_SCORE = "z_score"             # Z-score标准化
    ROBUST = "robust"               # 鲁棒归一化（基于中位数）
    ADAPTIVE = "adaptive"           # 自适应归一化


class MultimodalFeatureNormalizer:
    """
    多模态特征归一化器
    为不同模态提供最适合的归一化策略
    """
    
    def __init__(self, 
                 visual_strategy: str = "adaptive",
                 audio_strategy: str = "robust", 
                 text_strategy: str = "layer_norm",
                 visual_dim: int = 4096,
                 audio_dim: int = 6373,
                 eps: float = 1e-8):
        """
        初始化多模态归一化器
        
        Args:
            visual_strategy: 视觉特征归一化策略
            audio_strategy: 音频特征归一化策略  
            text_strategy: 文本特征归一化策略
            visual_dim: 视觉特征维度
            audio_dim: 音频特征维度
            eps: 数值稳定性参数
        """
        self.visual_strategy = NormalizationStrategy(visual_strategy)
        self.audio_strategy = NormalizationStrategy(audio_strategy)
        self.text_strategy = NormalizationStrategy(text_strategy)
        
        self.visual_dim = visual_dim
        self.audio_dim = audio_dim
        self.eps = eps
        
        # 统计信息缓存（用于批次归一化）
        self.visual_stats = {"mean": None, "std": None, "min": None, "max": None}
        self.audio_stats = {"mean": None, "std": None, "min": None, "max": None}
        
        # 可学习的归一化参数
        self.visual_normalizer = self._create_normalizer(visual_dim, visual_strategy)
        self.audio_normalizer = self._create_normalizer(audio_dim, audio_strategy)
    
    def _create_normalizer(self, dim: int, strategy: str) -> Optional[nn.Module]:
        """创建可学习的归一化层"""
        if strategy == "batch_norm":
            return nn.BatchNorm1d(dim)
        elif strategy == "layer_norm":
            return nn.LayerNorm(dim)
        else:
            return None
    
    def normalize_visual_features(self, 
                                 visual_features: torch.Tensor,
                                 update_stats: bool = True) -> torch.Tensor:
        """
        归一化视觉特征
        
        Args:
            visual_features: 视觉特征 [batch_size, seq_len, visual_dim] 或 [seq_len, visual_dim]
            update_stats: 是否更新统计信息
            
        Returns:
            normalized_features: 归一化后的视觉特征
        """
        if visual_features.numel() == 0:
            return visual_features
            
        return self._apply_normalization(
            visual_features, 
            self.visual_strategy, 
            self.visual_normalizer,
            self.visual_stats,
            update_stats,
            modality="visual"
        )
    
    def normalize_audio_features(self, 
                                audio_features: torch.Tensor,
                                update_stats: bool = True) -> torch.Tensor:
        """
        归一化音频特征
        
        Args:
            audio_features: 音频特征 [batch_size, seq_len, audio_dim] 或 [seq_len, audio_dim]
            update_stats: 是否更新统计信息
            
        Returns:
            normalized_features: 归一化后的音频特征
        """
        if audio_features.numel() == 0:
            return audio_features
            
        return self._apply_normalization(
            audio_features, 
            self.audio_strategy, 
            self.audio_normalizer,
            self.audio_stats,
            update_stats,
            modality="audio"
        )
    
    def _apply_normalization(self, 
                           features: torch.Tensor,
                           strategy: NormalizationStrategy,
                           normalizer: Optional[nn.Module],
                           stats: Dict,
                           update_stats: bool,
                           modality: str) -> torch.Tensor:
        """应用指定的归一化策略"""
        
        if strategy == NormalizationStrategy.NONE:
            return features
        
        # 处理零向量（无效特征）
        valid_mask = torch.norm(features, dim=-1, keepdim=True) > self.eps
        
        if strategy == NormalizationStrategy.L2_NORM:
            return self._l2_normalize(features, valid_mask)
        
        elif strategy == NormalizationStrategy.BATCH_NORM:
            return self._batch_normalize(features, normalizer, valid_mask)
        
        elif strategy == NormalizationStrategy.LAYER_NORM:
            return self._layer_normalize(features, normalizer, valid_mask)
        
        elif strategy == NormalizationStrategy.MIN_MAX:
            return self._min_max_normalize(features, stats, update_stats, valid_mask)
        
        elif strategy == NormalizationStrategy.Z_SCORE:
            return self._z_score_normalize(features, stats, update_stats, valid_mask)
        
        elif strategy == NormalizationStrategy.ROBUST:
            return self._robust_normalize(features, stats, update_stats, valid_mask)
        
        elif strategy == NormalizationStrategy.ADAPTIVE:
            return self._adaptive_normalize(features, stats, update_stats, valid_mask, modality)
        
        else:
            return features
    
    def _l2_normalize(self, features: torch.Tensor, valid_mask: torch.Tensor) -> torch.Tensor:
        """L2归一化"""
        normalized = F.normalize(features, p=2, dim=-1)
        # 保持无效特征为零
        return normalized * valid_mask
    
    def _batch_normalize(self, features: torch.Tensor, normalizer: nn.Module, valid_mask: torch.Tensor) -> torch.Tensor:
        """批归一化"""
        if normalizer is None:
            return features
        
        original_shape = features.shape
        if features.dim() > 2:
            features = features.view(-1, features.size(-1))
            valid_mask = valid_mask.view(-1, 1)
        
        # 只对有效特征进行归一化
        normalized = features.clone()
        valid_indices = valid_mask.squeeze(-1) > 0
        
        if valid_indices.any():
            normalized[valid_indices] = normalizer(features[valid_indices])
        
        return normalized.view(original_shape)
    
    def _layer_normalize(self, features: torch.Tensor, normalizer: nn.Module, valid_mask: torch.Tensor) -> torch.Tensor:
        """层归一化"""
        if normalizer is None:
            # 手动层归一化
            mean = features.mean(dim=-1, keepdim=True)
            std = features.std(dim=-1, keepdim=True)
            normalized = (features - mean) / (std + self.eps)
        else:
            normalized = normalizer(features)
        
        # 保持无效特征为零
        return normalized * valid_mask
    
    def _min_max_normalize(self, features: torch.Tensor, stats: Dict, update_stats: bool, valid_mask: torch.Tensor) -> torch.Tensor:
        """最小-最大归一化"""
        if update_stats or stats["min"] is None:
            # 只考虑有效特征计算统计信息
            valid_features = features[valid_mask.squeeze(-1) > 0]
            if valid_features.numel() > 0:
                stats["min"] = valid_features.min(dim=0, keepdim=True)[0]
                stats["max"] = valid_features.max(dim=0, keepdim=True)[0]
            else:
                stats["min"] = torch.zeros_like(features[0:1])
                stats["max"] = torch.ones_like(features[0:1])
        
        # 避免除零
        range_val = stats["max"] - stats["min"]
        range_val = torch.where(range_val < self.eps, torch.ones_like(range_val), range_val)
        
        normalized = (features - stats["min"]) / range_val
        return normalized * valid_mask
    
    def _z_score_normalize(self, features: torch.Tensor, stats: Dict, update_stats: bool, valid_mask: torch.Tensor) -> torch.Tensor:
        """Z-score标准化"""
        if update_stats or stats["mean"] is None:
            # 只考虑有效特征计算统计信息
            valid_features = features[valid_mask.squeeze(-1) > 0]
            if valid_features.numel() > 0:
                stats["mean"] = valid_features.mean(dim=0, keepdim=True)
                stats["std"] = valid_features.std(dim=0, keepdim=True)
            else:
                stats["mean"] = torch.zeros_like(features[0:1])
                stats["std"] = torch.ones_like(features[0:1])
        
        # 避免除零
        std = torch.where(stats["std"] < self.eps, torch.ones_like(stats["std"]), stats["std"])
        
        normalized = (features - stats["mean"]) / std
        return normalized * valid_mask
    
    def _robust_normalize(self, features: torch.Tensor, stats: Dict, update_stats: bool, valid_mask: torch.Tensor) -> torch.Tensor:
        """鲁棒归一化（基于中位数和MAD）"""
        if update_stats or stats.get("median") is None:
            # 只考虑有效特征计算统计信息
            valid_features = features[valid_mask.squeeze(-1) > 0]
            if valid_features.numel() > 0:
                stats["median"] = valid_features.median(dim=0, keepdim=True)[0]
                # 计算中位数绝对偏差(MAD)
                mad = torch.median(torch.abs(valid_features - stats["median"]), dim=0, keepdim=True)[0]
                stats["mad"] = torch.where(mad < self.eps, torch.ones_like(mad), mad)
            else:
                stats["median"] = torch.zeros_like(features[0:1])
                stats["mad"] = torch.ones_like(features[0:1])
        
        normalized = (features - stats["median"]) / stats["mad"]
        return normalized * valid_mask
    
    def _adaptive_normalize(self, features: torch.Tensor, stats: Dict, update_stats: bool, valid_mask: torch.Tensor, modality: str) -> torch.Tensor:
        """自适应归一化：根据特征分布自动选择最佳策略"""
        if features.numel() == 0:
            return features
        
        valid_features = features[valid_mask.squeeze(-1) > 0]
        if valid_features.numel() == 0:
            return features
        
        # 分析特征分布特性
        mean_val = valid_features.mean()
        std_val = valid_features.std()
        skewness = self._compute_skewness(valid_features)
        kurtosis = self._compute_kurtosis(valid_features)
        
        # 根据分布特性选择归一化策略
        if abs(skewness) > 2.0 or kurtosis > 7.0:
            # 高偏度或高峰度：使用鲁棒归一化
            return self._robust_normalize(features, stats, update_stats, valid_mask)
        elif std_val < 0.1 * abs(mean_val):
            # 低方差：使用最小-最大归一化
            return self._min_max_normalize(features, stats, update_stats, valid_mask)
        elif modality == "visual" and torch.norm(valid_features, dim=-1).std() > 0.5:
            # 视觉特征且范数变化大：使用L2归一化
            return self._l2_normalize(features, valid_mask)
        else:
            # 默认使用Z-score标准化
            return self._z_score_normalize(features, stats, update_stats, valid_mask)
    
    def _compute_skewness(self, features: torch.Tensor) -> float:
        """计算偏度"""
        mean = features.mean()
        std = features.std()
        if std < self.eps:
            return 0.0
        
        centered = features - mean
        skew = torch.mean(torch.pow(centered / std, 3))
        return float(skew.item())
    
    def _compute_kurtosis(self, features: torch.Tensor) -> float:
        """计算峰度"""
        mean = features.mean()
        std = features.std()
        if std < self.eps:
            return 0.0
        
        centered = features - mean
        kurt = torch.mean(torch.pow(centered / std, 4)) - 3.0  # 减去3得到超额峰度
        return float(kurt.item())
    
    def get_normalization_info(self) -> Dict:
        """获取归一化信息"""
        return {
            "visual_strategy": self.visual_strategy.value,
            "audio_strategy": self.audio_strategy.value,
            "text_strategy": self.text_strategy.value,
            "visual_stats": self.visual_stats,
            "audio_stats": self.audio_stats
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.visual_stats = {"mean": None, "std": None, "min": None, "max": None}
        self.audio_stats = {"mean": None, "std": None, "min": None, "max": None}


def create_normalizer_from_config(config) -> MultimodalFeatureNormalizer:
    """从配置创建归一化器"""
    visual_strategy = getattr(config, 'visual_norm_strategy', 'adaptive')
    audio_strategy = getattr(config, 'audio_norm_strategy', 'robust')
    text_strategy = getattr(config, 'text_norm_strategy', 'layer_norm')
    
    return MultimodalFeatureNormalizer(
        visual_strategy=visual_strategy,
        audio_strategy=audio_strategy,
        text_strategy=text_strategy,
        visual_dim=getattr(config, 'visual_dim', 4096),
        audio_dim=getattr(config, 'audio_dim', 6373)
    )