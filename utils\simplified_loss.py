#!/usr/bin/env python3
"""
简化的损失函数模块
专注于核心ECPE任务，去除复杂的启发式规则
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional


class SimplifiedECPELoss(nn.Module):
    """
    简化的ECPE损失函数
    专注于核心目标：准确生成情感-原因对序列
    """
    
    def __init__(self, token2idx: Dict[str, int], label_smoothing: float = 0.1):
        super().__init__()
        self.token2idx = token2idx
        self.pad_idx = token2idx.get('<pad>', 0)
        self.sos_idx = token2idx.get('<sos>', 1)
        self.eos_idx = token2idx.get('<eos>', 2)
        self.sep_idx = token2idx.get('<sep>', 3)
        
        # 使用标签平滑的交叉熵损失
        self.criterion = nn.CrossEntropyLoss(
            ignore_index=self.pad_idx,
            label_smoothing=label_smoothing
        )
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算损失
        
        Args:
            logits: [batch_size, seq_len, vocab_size]
            targets: [batch_size, seq_len]
        
        Returns:
            loss: 标量损失值
        """
        batch_size, seq_len, vocab_size = logits.shape
        
        # 计算基础交叉熵损失
        loss = self.criterion(
            logits.view(-1, vocab_size), 
            targets.view(-1)
        )
        
        return loss


class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, ignore_index: int = 0):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Focal Loss
        
        Args:
            logits: [batch_size * seq_len, vocab_size]
            targets: [batch_size * seq_len]
        """
        # 创建掩码，忽略padding tokens
        mask = targets != self.ignore_index
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 只计算非padding位置的损失
        logits_masked = logits[mask]
        targets_masked = targets[mask]
        
        # 计算交叉熵
        ce_loss = F.cross_entropy(logits_masked, targets_masked, reduction='none')
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # 计算focal loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


class LabelSmoothingCrossEntropy(nn.Module):
    """
    Label Smoothing Cross Entropy Loss
    """
    
    def __init__(self, smoothing: float = 0.1, ignore_index: int = 0):
        super().__init__()
        self.smoothing = smoothing
        self.ignore_index = ignore_index
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算标签平滑交叉熵损失
        """
        # 创建掩码
        mask = targets != self.ignore_index
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 只计算非padding位置的损失
        logits_masked = logits[mask]
        targets_masked = targets[mask]
        
        vocab_size = logits_masked.size(-1)
        
        # 创建平滑标签
        smooth_targets = torch.zeros_like(logits_masked)
        smooth_targets.fill_(self.smoothing / (vocab_size - 1))
        smooth_targets.scatter_(1, targets_masked.unsqueeze(1), 1.0 - self.smoothing)
        
        # 计算KL散度
        log_probs = F.log_softmax(logits_masked, dim=-1)
        loss = F.kl_div(log_probs, smooth_targets, reduction='batchmean')
        
        return loss


class StructureAwareLoss(nn.Module):
    """
    结构感知损失函数
    对重要的结构token（如<sep>、<eos>）给予额外关注
    """
    
    def __init__(self, token2idx: Dict[str, int], structure_weight: float = 0.2):
        super().__init__()
        self.token2idx = token2idx
        self.pad_idx = token2idx.get('<pad>', 0)
        self.sep_idx = token2idx.get('<sep>', 3)
        self.eos_idx = token2idx.get('<eos>', 2)
        self.structure_weight = structure_weight
        
        # 基础损失函数
        self.base_criterion = nn.CrossEntropyLoss(ignore_index=self.pad_idx)
        
        # 重要token集合
        self.important_tokens = {self.sep_idx, self.eos_idx}
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算结构感知损失
        """
        batch_size, seq_len, vocab_size = logits.shape
        
        # 1. 基础交叉熵损失
        base_loss = self.base_criterion(
            logits.view(-1, vocab_size), 
            targets.view(-1)
        )
        
        # 2. 结构损失：对重要token加权
        structure_loss = self._compute_structure_loss(logits, targets)
        
        # 3. 组合损失
        total_loss = base_loss + self.structure_weight * structure_loss
        
        return total_loss
    
    def _compute_structure_loss(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """计算结构损失"""
        structure_loss = 0.0
        count = 0
        
        for token_idx in self.important_tokens:
            # 找到目标中包含该token的位置
            token_mask = (targets == token_idx)
            if token_mask.any():
                # 提取对应位置的logits和targets
                token_positions = torch.nonzero(token_mask, as_tuple=False)
                for pos in token_positions:
                    b, t = pos[0], pos[1]
                    token_logits = logits[b, t].unsqueeze(0)  # [1, vocab_size]
                    token_target = targets[b, t].unsqueeze(0)  # [1]
                    
                    # 计算该位置的损失
                    token_loss = F.cross_entropy(token_logits, token_target)
                    structure_loss += token_loss
                    count += 1
        
        # 平均化
        if count > 0:
            structure_loss = structure_loss / count
        else:
            structure_loss = torch.tensor(0.0, device=logits.device)
        
        return structure_loss


def create_loss_function(loss_type: str, token2idx: Dict[str, int], **kwargs) -> nn.Module:
    """
    创建损失函数
    
    Args:
        loss_type: 损失函数类型
        token2idx: token到索引的映射
        **kwargs: 其他参数
    
    Returns:
        损失函数实例
    """
    if loss_type == 'simplified':
        return SimplifiedECPELoss(
            token2idx=token2idx,
            label_smoothing=kwargs.get('label_smoothing', 0.1)
        )
    
    elif loss_type == 'focal':
        return FocalLoss(
            alpha=kwargs.get('alpha', 1.0),
            gamma=kwargs.get('gamma', 2.0),
            ignore_index=token2idx.get('<pad>', 0)
        )
    
    elif loss_type == 'label_smoothing':
        return LabelSmoothingCrossEntropy(
            smoothing=kwargs.get('smoothing', 0.1),
            ignore_index=token2idx.get('<pad>', 0)
        )
    
    elif loss_type == 'structure_aware':
        return StructureAwareLoss(
            token2idx=token2idx,
            structure_weight=kwargs.get('structure_weight', 0.2)
        )
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


# 便利函数
def simplified_loss(logits: torch.Tensor, targets: torch.Tensor, token2idx: Dict[str, int]) -> torch.Tensor:
    """简化损失函数的便利接口"""
    criterion = SimplifiedECPELoss(token2idx)
    return criterion(logits, targets)


def focal_loss(logits: torch.Tensor, targets: torch.Tensor, 
               alpha: float = 1.0, gamma: float = 2.0, ignore_index: int = 0) -> torch.Tensor:
    """Focal损失函数的便利接口"""
    criterion = FocalLoss(alpha, gamma, ignore_index)
    return criterion(logits, targets)
