#!/usr/bin/env python3
"""
简化的命令行参数解析器
专注于核心训练参数
"""

import argparse
from config_simplified import SimplifiedConfig, set_simplified_config


def create_simplified_parser():
    """创建简化的命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="简化的多模态Graph2ECPE训练脚本",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 数据集参数
    parser.add_argument(
        '--dataset', 
        type=str, 
        default='meld',
        choices=['meld', 'iemocap'],
        help='数据集名称'
    )
    
    # 模型参数
    parser.add_argument(
        '--hidden_size', 
        type=int, 
        default=256,
        help='隐藏层大小'
    )
    
    parser.add_argument(
        '--gnn_layers', 
        type=int, 
        default=2,
        help='GNN层数'
    )
    
    parser.add_argument(
        '--decoder_layers', 
        type=int, 
        default=2,
        help='解码器层数'
    )
    
    # 训练参数
    parser.add_argument(
        '--batch_size', 
        type=int, 
        default=8,
        help='批次大小'
    )
    
    parser.add_argument(
        '--learning_rate', 
        type=float, 
        default=2e-5,
        help='学习率'
    )
    
    parser.add_argument(
        '--epochs', 
        type=int, 
        default=15,
        help='训练轮数'
    )
    
    parser.add_argument(
        '--weight_decay', 
        type=float, 
        default=0.01,
        help='权重衰减'
    )
    
    # 损失函数参数
    parser.add_argument(
        '--loss_type', 
        type=str, 
        default='simplified',
        choices=['simplified', 'focal', 'label_smoothing', 'structure_aware'],
        help='损失函数类型'
    )
    
    parser.add_argument(
        '--label_smoothing', 
        type=float, 
        default=0.1,
        help='标签平滑参数'
    )
    
    # 实验参数
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='output',
        help='输出目录'
    )
    
    parser.add_argument(
        '--seed', 
        type=int, 
        default=42,
        help='随机种子'
    )
    
    parser.add_argument(
        '--device', 
        type=str, 
        default='cuda',
        choices=['cuda', 'cpu'],
        help='训练设备'
    )
    
    # 预设配置
    parser.add_argument(
        '--config_preset', 
        type=str, 
        default='default',
        choices=['default', 'lightweight', 'performance'],
        help='配置预设'
    )
    
    # 调试参数
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--dry_run', 
        action='store_true',
        help='干运行模式（不实际训练）'
    )
    
    return parser


def parse_simplified_args():
    """解析简化的命令行参数"""
    parser = create_simplified_parser()
    args = parser.parse_args()
    return args


def create_config_from_args():
    """从命令行参数创建配置"""
    args = parse_simplified_args()
    
    # 根据预设创建配置
    if args.config_preset == 'lightweight':
        from config_simplified import get_lightweight_config
        config = get_lightweight_config()
    elif args.config_preset == 'performance':
        from config_simplified import get_performance_config
        config = get_performance_config()
    else:
        config = SimplifiedConfig(dataset_name=args.dataset)
    
    # 从命令行参数更新配置
    config.update_from_args(args)
    
    # 设置为全局配置
    set_simplified_config(config)
    
    return config, args


def print_simplified_args(args):
    """打印命令行参数"""
    print("\n📋 命令行参数:")
    print("-" * 30)
    
    # 数据集参数
    print(f"数据集: {args.dataset}")
    
    # 模型参数
    print(f"隐藏层大小: {args.hidden_size}")
    print(f"GNN层数: {args.gnn_layers}")
    print(f"解码器层数: {args.decoder_layers}")
    
    # 训练参数
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.learning_rate}")
    print(f"训练轮数: {args.epochs}")
    print(f"损失函数: {args.loss_type}")
    
    # 实验参数
    print(f"输出目录: {args.output_dir}")
    print(f"随机种子: {args.seed}")
    print(f"设备: {args.device}")
    print(f"配置预设: {args.config_preset}")
    
    if args.debug:
        print("调试模式: 启用")
    if args.dry_run:
        print("干运行模式: 启用")
    
    print("-" * 30)


# 便利函数
def quick_config(dataset='meld', batch_size=8, learning_rate=2e-5, epochs=15):
    """快速创建配置"""
    config = SimplifiedConfig(dataset_name=dataset)
    config.training.batch_size = batch_size
    config.training.learning_rate = learning_rate
    config.training.epochs = epochs
    return config


def debug_config():
    """调试配置"""
    config = SimplifiedConfig()
    config.model.hidden_size = 64
    config.model.gnn_layers = 1
    config.model.decoder_layers = 1
    config.training.batch_size = 2
    config.training.epochs = 2
    return config


if __name__ == "__main__":
    # 测试参数解析
    config, args = create_config_from_args()
    print_simplified_args(args)
    config.print_config()
