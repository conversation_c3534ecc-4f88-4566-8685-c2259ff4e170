"""
修复后的多模态Graph2Seq模型用于情感原因对提取
解决了维度不匹配和图构建失败的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoConfig
from typing import List, Tuple, Optional

from .graph2seq_ecpe import Graph2SeqECPE
from .graph_decoder import Graph2SeqDecoder
from utils.graphify import create_utterance_features


class MultimodalGraph2SeqECPEFixed(nn.Module):
    """
    修复后的多模态Graph2Seq模型
    简化了图构建策略，修复了维度不匹配问题
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 加载预训练语言模型
        PLM_path = "/userdata2/fengweijie/" + config.plm_model
        self.plm_config = AutoConfig.from_pretrained(PLM_path, local_files_only=True)
        self.plm = AutoModel.from_pretrained(PLM_path, local_files_only=True)
        
        # 如果指定，冻结预训练模型参数
        if getattr(config, 'freeze_plm', False):
            for param in self.plm.parameters():
                param.requires_grad = False
        
        # 话语特征投影层
        self.utt_proj = nn.Linear(self.plm_config.hidden_size, config.hidden_size)
        
        # 多模态配置
        self.modalities = getattr(config, 'modalities', ['text', 'visual', 'audio'])
        self.visual_dim = getattr(config, 'visual_dim', 4096)
        self.audio_dim = getattr(config, 'audio_dim', 6373)
        self.use_multimodal = getattr(config, 'use_multimodal', True)

        # 修复：在初始化时创建所有投影层
        if self.use_multimodal:
            self.modal_projections = nn.ModuleDict()

            if 'visual' in self.modalities:
                self.modal_projections['visual'] = nn.Linear(self.visual_dim, config.hidden_size)

            if 'audio' in self.modalities:
                self.modal_projections['audio'] = nn.Linear(self.audio_dim, config.hidden_size)
        
        # 简化的多模态图编码器
        if self.use_multimodal:
            self.multimodal_encoder = SimpleMultimodalGNN(
                input_dim=config.hidden_size,
                hidden_dim=config.hidden_size,
                output_dim=config.hidden_size,
                num_layers=getattr(config, 'enhanced_gnn_layers', 3),
                num_edge_types=5,  # 简化边类型
                num_modalities=len(self.modalities),
                dropout=config.dropout
            )
        else:
            # 使用原始的单模态编码器
            from .graph_encoders import GNN
            self.graph_encoder = GNN(
                in_channels=config.hidden_size,
                hidden_channels=config.hidden_size,
                out_channels=config.hidden_size,
                num_layers=config.gnn_layers,
                dropout=config.dropout,
                num_edge_types=5,
                pooling_method='attention'
            )
        
        # 图解码器
        vocab_size = getattr(config, 'vocab_size', 1000)
        self.graph_decoder = Graph2SeqDecoder(
            hidden_size=config.hidden_size,
            vocab_size=vocab_size,
            num_layers=config.decoder_layers,
            dropout=config.dropout,
            max_length=config.max_target_length
        )
    
    def forward(self, batch, target_seq=None, teacher_forcing_ratio=0.5):
        """
        前向传播 - 修复版本
        """
        device = next(self.parameters()).device
        
        # 解析批次数据
        if len(batch) >= 12:  # 多模态数据格式
            (dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, 
             utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch, 
             emotion_cause_edge_indices_batch, visual_features_batch, audio_features_batch) = batch
        else:
            # 兼容原始单模态格式
            (dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, 
             utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch) = batch[:9]
            emotion_cause_edge_indices_batch = batch[9] if len(batch) > 9 else [[] for _ in range(len(edge_index_batch))]
            visual_features_batch = None
            audio_features_batch = None
        
        # 将数据移至设备
        dialog_tokens = dialog_tokens.to(device)
        dialog_uttid = dialog_uttid.to(device)
        dialog_mask = dialog_mask.to(device)
        utt_mask = utt_mask.to(device)
        utt_speakers = utt_speakers.to(device)
        utt_emotions = utt_emotions.to(device)
        
        # 通过预训练模型获取token嵌入
        with torch.set_grad_enabled(not getattr(self.config, 'freeze_plm', False)):
            plm_outputs = self.plm(
                input_ids=dialog_tokens,
                attention_mask=dialog_mask,
                return_dict=True
            )
            token_embs = plm_outputs.last_hidden_state
        
        # 创建话语级特征
        utterance_features, lengths = create_utterance_features(
            token_embs, dialog_uttid, dialog_mask, self.config.hidden_size, device
        )
        
        # 投影话语特征
        projected_features = self.utt_proj(utterance_features)
        
        if self.use_multimodal and visual_features_batch is not None and audio_features_batch is not None:
            # 使用修复后的多模态编码
            node_embeddings, graph_embedding = self._fixed_multimodal_encoding(
                projected_features, visual_features_batch, audio_features_batch,
                utt_speakers, lengths, device
            )
        else:
            # 使用原始单模态编码
            from utils.graphify import batch_graphify
            node_features, edge_index, edge_norm, edge_type, batch_indices = batch_graphify(
                projected_features, lengths, utt_speakers, 
                edge_index_batch, edge_type_batch, edge_norm_batch, device
            )
            
            # 图编码
            node_embeddings, graph_embedding = self.graph_encoder(
                node_features, edge_index, edge_type, edge_norm, batch_indices
            )
        
        # 准备情感-原因节点信息
        batch_indices = []
        for batch_idx, length in enumerate(lengths):
            batch_indices.extend([batch_idx] * length)
        batch_indices = torch.tensor(batch_indices, dtype=torch.long, device=device)

        emotion_cause_nodes = self._prepare_emotion_cause_nodes(
            emotion_cause_edge_indices_batch, edge_index_batch, batch_indices, device
        )
        
        # 解码生成情感-原因对
        decoder_outputs, attention_weights = self.graph_decoder(
            node_embeddings, graph_embedding, target_seq, batch_indices, 
            teacher_forcing_ratio, emotion_cause_nodes
        )
        
        return decoder_outputs, attention_weights

    def _fixed_multimodal_encoding(self,
                                  text_features: torch.Tensor,
                                  visual_features: List[torch.Tensor],
                                  audio_features: List[torch.Tensor],
                                  utt_speakers: torch.Tensor,
                                  lengths: List[int],
                                  device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        修复后的多模态编码方法
        """
        total_length = sum(lengths)
        
        # 1. 处理多模态特征 - 修复维度问题
        processed_visual = self._process_visual_features_fixed(visual_features, device)
        processed_audio = self._process_audio_features_fixed(audio_features, device)
        
        # 2. 创建特征列表 - 修复维度不匹配问题
        features_list = []
        modal_ids = []
        
        # 确保文本特征是2D的
        if text_features.dim() == 3:
            # 如果是3D [batch_size, seq_len, hidden_dim]，需要重新整形
            text_features = text_features.view(-1, text_features.size(-1))
        elif text_features.dim() == 1:
            text_features = text_features.unsqueeze(0)
        
        # 确保文本特征长度正确
        if text_features.size(0) != total_length:
            if text_features.size(0) > total_length:
                text_features = text_features[:total_length]
            else:
                # 重复最后一个特征
                last_feat = text_features[-1:].repeat(total_length - text_features.size(0), 1)
                text_features = torch.cat([text_features, last_feat], dim=0)
        
        features_list.append(text_features)  # 文本特征 [total_length, hidden_dim]
        modal_ids.extend([0] * total_length)  # 文本模态ID = 0
        
        # 添加视觉特征
        if processed_visual:
            visual_concat = torch.cat(processed_visual, dim=0)  # [total_utterances, visual_dim]
            
            # 确保是2D张量
            if visual_concat.dim() != 2:
                visual_concat = visual_concat.view(-1, visual_concat.size(-1))
            
            if visual_concat.size(0) != total_length:
                # 调整大小以匹配话语数量
                if visual_concat.size(0) > total_length:
                    visual_concat = visual_concat[:total_length]
                else:
                    # 重复最后一个特征
                    last_feat = visual_concat[-1:].repeat(total_length - visual_concat.size(0), 1)
                    visual_concat = torch.cat([visual_concat, last_feat], dim=0)
            
            visual_projected = self.modal_projections['visual'](visual_concat)
            features_list.append(visual_projected)  # [total_length, hidden_dim]
            modal_ids.extend([1] * total_length)  # 视觉模态ID = 1
        
        # 添加音频特征
        if processed_audio:
            audio_concat = torch.cat(processed_audio, dim=0)  # [total_utterances, audio_dim]
            
            # 确保是2D张量
            if audio_concat.dim() != 2:
                audio_concat = audio_concat.view(-1, audio_concat.size(-1))
            
            if audio_concat.size(0) != total_length:
                # 调整大小以匹配话语数量
                if audio_concat.size(0) > total_length:
                    audio_concat = audio_concat[:total_length]
                else:
                    # 重复最后一个特征
                    last_feat = audio_concat[-1:].repeat(total_length - audio_concat.size(0), 1)
                    audio_concat = torch.cat([audio_concat, last_feat], dim=0)
            
            audio_projected = self.modal_projections['audio'](audio_concat)
            features_list.append(audio_projected)  # [total_length, hidden_dim]
            modal_ids.extend([2] * total_length)  # 音频模态ID = 2
        
        # 3. 验证所有特征具有相同的维度
        # 确保所有特征都是2D且具有相同的第二维
        target_hidden_dim = features_list[0].size(-1)
        for i, feat in enumerate(features_list):
            if feat.dim() != 2:
                feat = feat.view(-1, feat.size(-1))
                features_list[i] = feat
            
            if feat.size(-1) != target_hidden_dim:
                # 使用线性层调整维度
                temp_proj = nn.Linear(feat.size(-1), target_hidden_dim).to(feat.device)
                features_list[i] = temp_proj(feat)
        
        # 创建简单的多模态图结构
        all_features = torch.cat(features_list, dim=0)  # [num_modalities * total_utterances, hidden_dim]
        modal_ids_tensor = torch.tensor(modal_ids, device=device, dtype=torch.long)
        
        edge_index, edge_weights, edge_types = self._create_simple_multimodal_graph(
            total_length, len(features_list), device
        )
        
        # 4. 使用简化的多模态GNN进行编码
        node_embeddings = self.multimodal_encoder(
            x=all_features,
            edge_index=edge_index,
            edge_type=edge_types,
            modal_ids=modal_ids_tensor,
            utterance_lengths=lengths
        )
        
        # 5. 提取文本模态的节点嵌入（用于后续解码）
        text_node_embeddings = node_embeddings[:total_length]
        
        # 6. 计算图级嵌入
        graph_embeddings = []
        start_idx = 0
        for length in lengths:
            end_idx = start_idx + length
            dialog_embedding = text_node_embeddings[start_idx:end_idx].mean(dim=0)
            graph_embeddings.append(dialog_embedding)
            start_idx = end_idx
        
        graph_embedding = torch.stack(graph_embeddings, dim=0)
        
        return text_node_embeddings, graph_embedding

    def _process_visual_features_fixed(self, visual_features: List[torch.Tensor], device: torch.device) -> List[torch.Tensor]:
        """修复后的视觉特征处理"""
        if not visual_features:
            return []
        
        processed_features = []
        for vf in visual_features:
            # 确保特征是2D的
            if vf.dim() == 1:
                vf = vf.unsqueeze(0)
            elif vf.dim() == 3:
                vf = vf.squeeze(0) if vf.size(0) == 1 else vf.view(vf.size(0), -1)
            elif vf.dim() > 3:
                vf = vf.view(vf.size(0), -1)
            
            # 移动到正确设备
            vf = vf.to(device)
            
            # 维度调整 - 使用学习的投影而不是简单截断/填充
            if vf.size(-1) != self.visual_dim:
                # 创建临时投影层
                temp_proj = nn.Linear(vf.size(-1), self.visual_dim).to(device)
                vf = temp_proj(vf)
            
            processed_features.append(vf)
        
        return processed_features

    def _process_audio_features_fixed(self, audio_features: List[torch.Tensor], device: torch.device) -> List[torch.Tensor]:
        """修复后的音频特征处理"""
        if not audio_features:
            return []
        
        processed_features = []
        for af in audio_features:
            # 确保特征是2D的
            if af.dim() == 1:
                af = af.unsqueeze(0)
            elif af.dim() == 3:
                af = af.squeeze(0) if af.size(0) == 1 else af.view(af.size(0), -1)
            elif af.dim() > 3:
                af = af.view(af.size(0), -1)
            
            # 移动到正确设备
            af = af.to(device)
            
            # 维度调整
            if af.size(-1) != self.audio_dim:
                # 创建临时投影层
                temp_proj = nn.Linear(af.size(-1), self.audio_dim).to(device)
                af = temp_proj(af)
            
            processed_features.append(af)
        
        return processed_features

    def _create_simple_multimodal_graph(self, total_length: int, num_modalities: int, device: torch.device):
        """创建简单的多模态图结构"""
        edge_indices = []
        edge_weights = []
        edge_types = []
        
        # 为每个模态创建内部连接
        for modal_id in range(num_modalities):
            modal_offset = modal_id * total_length
            
            # 自连接
            for i in range(total_length):
                node_idx = modal_offset + i
                edge_indices.append([node_idx, node_idx])
                edge_weights.append(1.0)
                edge_types.append(0)  # 自连接
            
            # 序列连接
            for i in range(total_length - 1):
                src_idx = modal_offset + i
                tgt_idx = modal_offset + i + 1
                
                edge_indices.append([src_idx, tgt_idx])
                edge_indices.append([tgt_idx, src_idx])
                edge_weights.extend([0.8, 0.8])
                edge_types.extend([1, 1])  # 序列连接
        
        # 跨模态连接（同一话语的不同模态）
        if num_modalities > 1:
            for i in range(total_length):
                for m1 in range(num_modalities):
                    for m2 in range(m1 + 1, num_modalities):
                        idx1 = m1 * total_length + i
                        idx2 = m2 * total_length + i
                        
                        edge_indices.append([idx1, idx2])
                        edge_indices.append([idx2, idx1])
                        edge_weights.extend([0.6, 0.6])
                        edge_types.extend([2, 2])  # 跨模态连接
        
        # 转换为张量
        if edge_indices:
            edge_index = torch.tensor(edge_indices, dtype=torch.long, device=device).t().contiguous()
            edge_weights = torch.tensor(edge_weights, dtype=torch.float, device=device)
            edge_types = torch.tensor(edge_types, dtype=torch.long, device=device)
        else:
            # 最小图结构
            total_nodes = num_modalities * total_length
            edge_index = torch.arange(total_nodes, device=device).unsqueeze(0).repeat(2, 1)
            edge_weights = torch.ones(total_nodes, device=device)
            edge_types = torch.zeros(total_nodes, dtype=torch.long, device=device)
        
        return edge_index, edge_weights, edge_types

    def _prepare_emotion_cause_nodes(self, emotion_cause_edge_indices_batch,
                                   edge_index_batch, batch_indices, device):
        """准备情感-原因节点信息"""
        emotion_cause_nodes = []

        if emotion_cause_edge_indices_batch:
            batch_to_nodes = {}
            for b in range(batch_indices.max().item() + 1):
                batch_nodes = (batch_indices == b).nonzero(as_tuple=True)[0]
                if batch_nodes.size(0) > 0:
                    batch_to_nodes[b] = (batch_nodes.min().item(), batch_nodes.max().item())
                else:
                    batch_to_nodes[b] = (0, 0)

            for b in range(len(emotion_cause_edge_indices_batch)):
                ec_nodes = set()
                if b < len(edge_index_batch) and b < len(emotion_cause_edge_indices_batch):
                    edges = edge_index_batch[b]
                    ec_indices = emotion_cause_edge_indices_batch[b]

                    if b in batch_to_nodes:
                        start_idx, _ = batch_to_nodes[b]
                        for idx in ec_indices:
                            if 0 <= idx < len(edges):
                                src, tgt = edges[idx]
                                ec_nodes.add(src + start_idx)

                emotion_cause_nodes.append(list(ec_nodes))

        while len(emotion_cause_nodes) < batch_indices.max().item() + 1:
            emotion_cause_nodes.append([])

        return emotion_cause_nodes

    def predict(self, batch, max_length=20):
        """预测方法"""
        self.eval()
        device = next(self.parameters()).device

        with torch.no_grad():
            sos_token_id = 1
            eos_token_id = 2
            pad_token_id = 0

            if len(batch) >= 12:
                utt_mask = batch[3]
            else:
                utt_mask = batch[3]
            batch_size = utt_mask.size(0)

            predictions = torch.full((batch_size, max_length), pad_token_id, dtype=torch.long, device=device)
            predictions[:, 0] = sos_token_id

            for step in range(1, max_length):
                current_seq = predictions[:, :step+1].clone()
                current_seq[:, step] = pad_token_id

                decoder_outputs, _ = self.forward(batch, current_seq, teacher_forcing_ratio=0.0)
                current_logits = decoder_outputs[:, step-1, :]
                
                # 简单的贪婪解码
                next_tokens = torch.argmax(current_logits, dim=-1)
                predictions[:, step] = next_tokens

                finished_mask = (next_tokens == eos_token_id)
                if torch.all(finished_mask):
                    break

            return predictions


class SimpleMultimodalGNN(nn.Module):
    """
    简化的多模态图神经网络
    避免复杂的动态参数创建
    """
    
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=3, 
                 num_edge_types=5, num_modalities=3, dropout=0.1):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # 简单的GCN层
        self.gnn_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_dim = hidden_dim
            layer_output_dim = hidden_dim if i < num_layers - 1 else output_dim
            
            self.gnn_layers.append(
                nn.Sequential(
                    nn.Linear(layer_input_dim, layer_output_dim),
                    nn.ReLU(),
                    nn.Dropout(dropout)
                )
            )
        
        # 输出投影
        self.output_projection = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, edge_index, edge_type, modal_ids=None, utterance_lengths=None):
        """简化的前向传播"""
        # 输入投影
        x = self.input_projection(x)
        x = self.dropout(x)
        
        # 简单的消息传递
        for i, layer in enumerate(self.gnn_layers):
            # 简单的邻居聚合
            if edge_index.size(1) > 0:
                # 获取源节点和目标节点
                src_nodes = x[edge_index[0]]
                tgt_indices = edge_index[1]
                
                # 简单的平均聚合
                aggregated = torch.zeros_like(x)
                for j in range(x.size(0)):
                    # 找到指向节点j的所有边
                    incoming_mask = (tgt_indices == j)
                    if incoming_mask.any():
                        incoming_features = src_nodes[incoming_mask]
                        aggregated[j] = incoming_features.mean(dim=0)
                    else:
                        aggregated[j] = x[j]  # 保持原特征
                
                # 应用层变换
                x = layer(aggregated + x)  # 残差连接
            else:
                # 没有边时直接应用层变换
                x = layer(x)
        
        # 最终投影
        if x.size(-1) != self.output_dim:
            x = self.output_projection(x)
        
        return x
