"""
邻居采样器模块

实现多种邻居采样策略，用于控制图神经网络的计算复杂度和提高训练效率。
"""

import torch
import torch.nn as nn
import numpy as np


class UniformNeighborSampler(nn.Module):
    """
    均匀邻居采样器
    
    从每个节点的邻居中均匀随机采样固定数量的邻居节点。
    这是最基础的采样策略，适用于大多数场景。
    """
    
    def __init__(self, sample_size=10):
        super().__init__()
        self.sample_size = sample_size
    
    def sample_neighbors(self, edge_index, num_nodes, node_idx=None):
        """
        均匀采样邻居节点
        
        Args:
            edge_index: 边索引 [2, num_edges]
            num_nodes: 节点总数
            node_idx: 需要采样邻居的节点索引，如果为None则对所有节点采样
            
        Returns:
            sampled_edge_index: 采样后的边索引 [2, num_sampled_edges]
            sampled_edge_mask: 被采样边的掩码 [num_edges]
        """
        if node_idx is None:
            node_idx = torch.arange(num_nodes, device=edge_index.device)
        
        # 构建邻接列表
        adj_list = self._build_adjacency_list(edge_index, num_nodes)
        
        sampled_edges = []
        sampled_edge_indices = []
        
        for node in node_idx:
            node = node.item() if hasattr(node, 'item') else node
            neighbors = adj_list.get(node, [])
            
            if len(neighbors) <= self.sample_size:
                # 如果邻居数量不超过采样大小，保留所有邻居
                sampled_neighbors = neighbors
            else:
                # 均匀随机采样
                indices = torch.randperm(len(neighbors))[:self.sample_size]
                sampled_neighbors = [neighbors[i] for i in indices]
            
            for neighbor, edge_idx in sampled_neighbors:
                sampled_edges.append([node, neighbor])
                sampled_edge_indices.append(edge_idx)
        
        return self._create_sampled_results(sampled_edges, sampled_edge_indices, edge_index)
    
    def _build_adjacency_list(self, edge_index, num_nodes):
        """构建邻接列表"""
        adj_list = {}
        for i in range(num_nodes):
            adj_list[i] = []
        
        for i in range(edge_index.size(1)):
            src, dst = edge_index[0, i].item(), edge_index[1, i].item()
            adj_list[src].append((dst, i))  # (邻居节点, 边索引)
        
        return adj_list
    
    def _create_sampled_results(self, sampled_edges, sampled_edge_indices, original_edge_index):
        """创建采样结果"""
        if sampled_edges:
            sampled_edge_index = torch.tensor(sampled_edges, device=original_edge_index.device).t()
            sampled_edge_mask = torch.zeros(original_edge_index.size(1), dtype=torch.bool, device=original_edge_index.device)
            sampled_edge_mask[sampled_edge_indices] = True
        else:
            sampled_edge_index = torch.empty((2, 0), device=original_edge_index.device, dtype=original_edge_index.dtype)
            sampled_edge_mask = torch.zeros(original_edge_index.size(1), dtype=torch.bool, device=original_edge_index.device)
        
        return sampled_edge_index, sampled_edge_mask


class ImportanceBasedNeighborSampler(UniformNeighborSampler):
    """
    基于重要性的邻居采样器
    
    根据节点的重要性分数进行采样，重要性可以基于度数、特征等计算。
    适用于需要保留重要邻居的场景。
    """
    
    def __init__(self, sample_size=10, importance_ratio=0.7):
        super().__init__(sample_size)
        self.importance_ratio = importance_ratio  # 重要邻居的比例
    
    def sample_neighbors(self, edge_index, num_nodes, node_features=None, node_idx=None):
        """
        基于重要性采样邻居节点
        
        Args:
            edge_index: 边索引 [2, num_edges]
            num_nodes: 节点总数
            node_features: 节点特征 [num_nodes, feature_dim]，用于计算重要性
            node_idx: 需要采样邻居的节点索引
            
        Returns:
            sampled_edge_index: 采样后的边索引
            sampled_edge_mask: 被采样边的掩码
        """
        if node_idx is None:
            node_idx = torch.arange(num_nodes, device=edge_index.device)
        
        # 计算节点重要性
        importance_scores = self._compute_node_importance(edge_index, num_nodes, node_features)
        
        # 构建带重要性的邻接列表
        adj_list = self._build_importance_adjacency_list(edge_index, num_nodes, importance_scores)
        
        sampled_edges = []
        sampled_edge_indices = []
        
        for node in node_idx:
            node = node.item() if hasattr(node, 'item') else node
            neighbors = adj_list.get(node, [])
            
            if len(neighbors) <= self.sample_size:
                sampled_neighbors = neighbors
            else:
                sampled_neighbors = self._importance_sampling(neighbors)
            
            for neighbor, edge_idx, _ in sampled_neighbors:
                sampled_edges.append([node, neighbor])
                sampled_edge_indices.append(edge_idx)
        
        return self._create_sampled_results(sampled_edges, sampled_edge_indices, edge_index)
    
    def _compute_node_importance(self, edge_index, num_nodes, node_features=None):
        """计算节点重要性分数"""
        # 基于度数的重要性
        degree_importance = torch.zeros(num_nodes, device=edge_index.device)
        for i in range(num_nodes):
            degree_importance[i] = (edge_index[0] == i).sum().float()
        
        # 基于特征的重要性（如果提供了节点特征）
        if node_features is not None:
            feature_importance = torch.norm(node_features, dim=1)
            # 归一化并组合
            degree_importance = degree_importance / (degree_importance.max() + 1e-8)
            feature_importance = feature_importance / (feature_importance.max() + 1e-8)
            importance_scores = 0.5 * degree_importance + 0.5 * feature_importance
        else:
            importance_scores = degree_importance / (degree_importance.max() + 1e-8)
        
        return importance_scores
    
    def _build_importance_adjacency_list(self, edge_index, num_nodes, importance_scores):
        """构建带重要性的邻接列表"""
        adj_list = {}
        for i in range(num_nodes):
            adj_list[i] = []
        
        for i in range(edge_index.size(1)):
            src, dst = edge_index[0, i].item(), edge_index[1, i].item()
            importance = importance_scores[dst].item()
            adj_list[src].append((dst, i, importance))  # (邻居节点, 边索引, 重要性)
        
        return adj_list
    
    def _importance_sampling(self, neighbors):
        """基于重要性进行采样"""
        # 按重要性排序
        neighbors.sort(key=lambda x: x[2], reverse=True)
        
        # 计算重要邻居和随机邻居的数量
        important_count = int(self.sample_size * self.importance_ratio)
        random_count = self.sample_size - important_count
        
        # 选择重要邻居
        important_neighbors = neighbors[:important_count]
        
        # 从剩余邻居中随机选择
        remaining_neighbors = neighbors[important_count:]
        if len(remaining_neighbors) > 0 and random_count > 0:
            random_indices = torch.randperm(len(remaining_neighbors))[:random_count]
            random_neighbors = [remaining_neighbors[i] for i in random_indices]
        else:
            random_neighbors = []
        
        return important_neighbors + random_neighbors


class ECPEAwareNeighborSampler(ImportanceBasedNeighborSampler):
    """
    ECPE任务感知的邻居采样器
    
    专门针对情感原因对提取任务设计，优先保留情感-原因相关的邻居。
    """
    
    def __init__(self, sample_size=10, emotion_cause_ratio=0.8):
        super().__init__(sample_size)
        self.emotion_cause_ratio = emotion_cause_ratio
    
    def sample_neighbors(self, edge_index, num_nodes, edge_type=None, emotion_mask=None, node_idx=None):
        """
        ECPE任务感知的邻居采样
        
        Args:
            edge_index: 边索引 [2, num_edges]
            num_nodes: 节点总数
            edge_type: 边类型 [num_edges]，用于识别情感-原因边
            emotion_mask: 情感节点掩码 [num_nodes]
            node_idx: 需要采样邻居的节点索引
            
        Returns:
            sampled_edge_index: 采样后的边索引
            sampled_edge_mask: 被采样边的掩码
        """
        if node_idx is None:
            node_idx = torch.arange(num_nodes, device=edge_index.device)
        
        # 构建ECPE感知的邻接列表
        adj_list = self._build_ecpe_adjacency_list(edge_index, num_nodes, edge_type, emotion_mask)
        
        sampled_edges = []
        sampled_edge_indices = []
        
        for node in node_idx:
            node = node.item() if hasattr(node, 'item') else node
            neighbors = adj_list.get(node, [])
            
            if len(neighbors) <= self.sample_size:
                sampled_neighbors = neighbors
            else:
                sampled_neighbors = self._ecpe_aware_sampling(neighbors)
            
            for neighbor, edge_idx, _ in sampled_neighbors:
                sampled_edges.append([node, neighbor])
                sampled_edge_indices.append(edge_idx)
        
        return self._create_sampled_results(sampled_edges, sampled_edge_indices, edge_index)
    
    def _build_ecpe_adjacency_list(self, edge_index, num_nodes, edge_type=None, emotion_mask=None):
        """构建ECPE感知的邻接列表"""
        adj_list = {}
        for i in range(num_nodes):
            adj_list[i] = []
        
        for i in range(edge_index.size(1)):
            src, dst = edge_index[0, i].item(), edge_index[1, i].item()
            
            # 计算ECPE重要性
            ecpe_importance = 0.0
            
            # 情感-原因边的重要性
            if edge_type is not None and edge_type[i].item() == 0:  # 假设0是情感-原因边类型
                ecpe_importance += 1.0
            
            # 情感节点的重要性
            if emotion_mask is not None and emotion_mask[dst]:
                ecpe_importance += 0.5
            
            adj_list[src].append((dst, i, ecpe_importance))
        
        return adj_list
    
    def _ecpe_aware_sampling(self, neighbors):
        """ECPE感知的采样策略"""
        # 按ECPE重要性排序
        neighbors.sort(key=lambda x: x[2], reverse=True)
        
        # 计算情感-原因相关邻居和其他邻居的数量
        ec_count = int(self.sample_size * self.emotion_cause_ratio)
        other_count = self.sample_size - ec_count
        
        # 选择情感-原因相关邻居
        ec_neighbors = neighbors[:ec_count]
        
        # 从剩余邻居中随机选择
        remaining_neighbors = neighbors[ec_count:]
        if len(remaining_neighbors) > 0 and other_count > 0:
            random_indices = torch.randperm(len(remaining_neighbors))[:other_count]
            other_neighbors = [remaining_neighbors[i] for i in random_indices]
        else:
            other_neighbors = []
        
        return ec_neighbors + other_neighbors


def create_neighbor_sampler(sampler_type='uniform', **kwargs):
    """
    工厂函数：创建邻居采样器
    
    Args:
        sampler_type: 采样器类型 ('uniform', 'importance', 'ecpe_aware')
        **kwargs: 采样器参数
        
    Returns:
        NeighborSampler: 邻居采样器实例
    """
    if sampler_type == 'uniform':
        return UniformNeighborSampler(**kwargs)
    elif sampler_type == 'importance':
        return ImportanceBasedNeighborSampler(**kwargs)
    elif sampler_type == 'ecpe_aware':
        return ECPEAwareNeighborSampler(**kwargs)
    else:
        raise ValueError(f"Unknown sampler type: {sampler_type}")
