#!/usr/bin/env python3
"""
学习率策略对比实验
比较OneCycle vs 分层学习率的效果
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
from pathlib import Path
import matplotlib.pyplot as plt

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimplifiedLoss(nn.Module):
    """简化的损失函数"""
    
    def __init__(self, token2idx):
        super().__init__()
        self.pad_idx = token2idx.get('<pad>', 0)
        self.criterion = nn.CrossEntropyLoss(ignore_index=self.pad_idx)
    
    def forward(self, logits, targets):
        batch_size, seq_len, vocab_size = logits.shape
        return self.criterion(logits.view(-1, vocab_size), targets.view(-1))


class OneCycleTrainer:
    """OneCycle学习率策略训练器"""
    
    def __init__(self, model, token2idx, config, max_lr=5e-5):
        self.model = model
        self.token2idx = token2idx
        self.config = config
        self.max_lr = max_lr
        
        # 损失函数
        self.criterion = SimplifiedLoss(token2idx)
        
        # 优化器
        self.optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=max_lr,
            weight_decay=0.01
        )
        
        # OneCycle调度器
        total_steps = config.training.epochs * 246  # 假设每个epoch 246步
        self.scheduler = torch.optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=max_lr,
            total_steps=total_steps,
            pct_start=0.1,  # 10%的步数用于warmup
            anneal_strategy='cos',
            div_factor=25.0,  # 初始lr = max_lr / div_factor
            final_div_factor=1e4  # 最终lr = max_lr / final_div_factor
        )
        
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.training_losses = []
        self.learning_rates = []
    
    def train_step(self, batch, epoch):
        """训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            target_seqs = target_seqs.to(device)
            
            if target_seqs.sum() == 0:
                return None
            
            # 动态teacher forcing
            teacher_forcing_ratio = max(0.5, 0.9 - epoch * 0.05)
            
            self.optimizer.zero_grad()
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            
            loss = self.criterion(decoder_outputs, target_seqs)
            
            if torch.isnan(loss) or torch.isinf(loss):
                return None
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            self.scheduler.step()  # OneCycle每步都更新
            
            # 记录学习率
            current_lr = self.scheduler.get_last_lr()[0]
            self.learning_rates.append(current_lr)
            
            return loss.item()
            
        except Exception as e:
            logger.warning(f"OneCycle训练步骤出错: {e}")
            return None


class LayeredLRTrainer:
    """分层学习率策略训练器"""
    
    def __init__(self, model, token2idx, config, plm_lr=1e-5, decoder_lr=1e-4):
        self.model = model
        self.token2idx = token2idx
        self.config = config
        self.plm_lr = plm_lr
        self.decoder_lr = decoder_lr
        
        # 损失函数
        self.criterion = SimplifiedLoss(token2idx)
        
        # 分层优化器
        plm_params = []
        decoder_params = []
        
        for name, param in model.named_parameters():
            if 'plm' in name:
                plm_params.append(param)
            else:
                decoder_params.append(param)
        
        self.optimizer = torch.optim.AdamW([
            {'params': plm_params, 'lr': plm_lr, 'weight_decay': 0.01},
            {'params': decoder_params, 'lr': decoder_lr, 'weight_decay': 0.001}
        ])
        
        # 热身调度器
        total_steps = config.training.epochs * 246
        warmup_steps = 500
        
        self.scheduler = torch.optim.lr_scheduler.LambdaLR(
            self.optimizer,
            lr_lambda=[
                self._get_warmup_lambda(warmup_steps, total_steps, 0),  # PLM层
                self._get_warmup_lambda(warmup_steps, total_steps, 1)   # 解码器层
            ]
        )
        
        self.best_f1 = 0.0
        self.patience_counter = 0
        self.training_losses = []
        self.learning_rates = []
        self.warmup_steps = warmup_steps
        self.step_count = 0
    
    def _get_warmup_lambda(self, warmup_steps, total_steps, param_group_idx):
        """获取热身lambda函数"""
        def lambda_func(step):
            if step < warmup_steps:
                # 热身阶段：线性增长
                return step / warmup_steps
            else:
                # 热身后：余弦衰减
                progress = (step - warmup_steps) / (total_steps - warmup_steps)
                return 0.5 * (1 + np.cos(np.pi * progress))
        return lambda_func
    
    def train_step(self, batch, epoch):
        """训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            target_seqs = target_seqs.to(device)
            
            if target_seqs.sum() == 0:
                return None
            
            # 动态teacher forcing
            teacher_forcing_ratio = max(0.5, 0.9 - epoch * 0.05)
            
            self.optimizer.zero_grad()
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            
            loss = self.criterion(decoder_outputs, target_seqs)
            
            if torch.isnan(loss) or torch.isinf(loss):
                return None
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            self.scheduler.step()
            
            # 记录学习率
            current_lrs = self.scheduler.get_last_lr()
            self.learning_rates.append(current_lrs)
            self.step_count += 1
            
            return loss.item()
            
        except Exception as e:
            logger.warning(f"分层LR训练步骤出错: {e}")
            return None


def create_datasets(config):
    """创建数据集"""
    data_path = config.dataset.get_data_path()
    
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        modalities=['text', 'visual', 'audio']
    )
    
    return train_dataset, dev_dataset


def evaluate_model(model, dev_loader, trainer, token2idx, idx2token):
    """评估模型"""
    model.eval()
    val_loss = 0.0
    val_batches = 0
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch in tqdm(dev_loader, desc="验证"):
            try:
                device = next(model.parameters()).device
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                target_seqs = target_seqs.to(device)
                
                decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                loss = trainer.criterion(decoder_outputs, target_seqs)
                val_loss += loss.item()
                val_batches += 1
                
                predictions = torch.argmax(decoder_outputs, dim=-1)
                all_predictions.append(predictions)
                all_targets.append(target_seqs)
                
            except Exception as e:
                logger.warning(f"验证批次出错: {e}")
                continue
    
    avg_val_loss = val_loss / max(1, val_batches)
    
    # 计算指标
    if all_predictions and all_targets:
        combined_predictions = torch.cat(all_predictions, dim=0)
        combined_targets = torch.cat(all_targets, dim=0)
        
        post_processor = UnifiedPostProcessor(mode='optimal')
        metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
    else:
        metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
    
    return avg_val_loss, metrics


def plot_learning_curves(onecycle_losses, layered_losses, onecycle_lrs, layered_lrs, save_path):
    """绘制学习曲线"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 训练损失对比
    ax1.plot(onecycle_losses, label='OneCycle', alpha=0.7)
    ax1.plot(layered_losses, label='Layered LR', alpha=0.7)
    ax1.set_title('Training Loss Comparison')
    ax1.set_xlabel('Steps')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)
    
    # OneCycle学习率曲线
    ax2.plot(onecycle_lrs)
    ax2.set_title('OneCycle Learning Rate Schedule')
    ax2.set_xlabel('Steps')
    ax2.set_ylabel('Learning Rate')
    ax2.grid(True)
    
    # 分层学习率曲线
    if layered_lrs and len(layered_lrs) > 0:
        plm_lrs = [lr[0] for lr in layered_lrs]
        decoder_lrs = [lr[1] for lr in layered_lrs]
        ax3.plot(plm_lrs, label='PLM LR', alpha=0.7)
        ax3.plot(decoder_lrs, label='Decoder LR', alpha=0.7)
    ax3.set_title('Layered Learning Rate Schedule')
    ax3.set_xlabel('Steps')
    ax3.set_ylabel('Learning Rate')
    ax3.legend()
    ax3.grid(True)
    
    # 损失平滑曲线对比
    if len(onecycle_losses) > 50:
        window = 50
        onecycle_smooth = np.convolve(onecycle_losses, np.ones(window)/window, mode='valid')
        layered_smooth = np.convolve(layered_losses, np.ones(window)/window, mode='valid')
        ax4.plot(onecycle_smooth, label='OneCycle (smoothed)', alpha=0.8)
        ax4.plot(layered_smooth, label='Layered LR (smoothed)', alpha=0.8)
        ax4.set_title('Smoothed Training Loss')
        ax4.set_xlabel('Steps')
        ax4.set_ylabel('Loss')
        ax4.legend()
        ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def run_comparison_experiment():
    """运行对比实验"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)
    
    # 实验配置
    config.training.batch_size = 4
    config.training.epochs = 10  # 较短的训练用于对比
    
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    
    logger.info("🔬 开始学习率策略对比实验")
    logger.info("📊 实验设置:")
    logger.info("  - OneCycle: max_lr=5e-5, 500步热身")
    logger.info("  - 分层LR: PLM=1e-5, Decoder=1e-4, 500步热身")
    logger.info(f"  - 训练轮数: {config.training.epochs}")
    logger.info(f"  - 批次大小: {config.training.batch_size}")
    
    set_seed(config.experiment.seed)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    train_dataset, dev_dataset = create_datasets(config)
    
    # 创建词汇表
    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=2
    )
    
    results = {}
    
    # 实验1: OneCycle策略
    logger.info("\n🚀 实验1: OneCycle学习率策略")
    model1 = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    trainer1 = OneCycleTrainer(model1, token2idx, config, max_lr=5e-5)
    
    for epoch in range(config.training.epochs):
        model1.train()
        epoch_losses = []
        
        for batch in tqdm(train_loader, desc=f"OneCycle Epoch {epoch+1}"):
            loss = trainer1.train_step(batch, epoch)
            if loss is not None:
                epoch_losses.append(loss)
                trainer1.training_losses.append(loss)
        
        if epoch_losses:
            avg_loss = np.mean(epoch_losses)
            logger.info(f"OneCycle Epoch {epoch+1}: Loss={avg_loss:.4f}")
    
    # 评估OneCycle
    val_loss1, metrics1 = evaluate_model(model1, dev_loader, trainer1, token2idx, idx2token)
    results['onecycle'] = {
        'val_loss': val_loss1,
        'metrics': metrics1,
        'training_losses': trainer1.training_losses,
        'learning_rates': trainer1.learning_rates
    }
    
    logger.info(f"OneCycle结果: F1={metrics1['f1']:.4f}, P={metrics1['precision']:.4f}, R={metrics1['recall']:.4f}")
    
    # 实验2: 分层学习率策略
    logger.info("\n🚀 实验2: 分层学习率策略")
    model2 = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    trainer2 = LayeredLRTrainer(model2, token2idx, config, plm_lr=1e-5, decoder_lr=1e-4)
    
    for epoch in range(config.training.epochs):
        model2.train()
        epoch_losses = []
        
        for batch in tqdm(train_loader, desc=f"Layered LR Epoch {epoch+1}"):
            loss = trainer2.train_step(batch, epoch)
            if loss is not None:
                epoch_losses.append(loss)
                trainer2.training_losses.append(loss)
        
        if epoch_losses:
            avg_loss = np.mean(epoch_losses)
            logger.info(f"Layered LR Epoch {epoch+1}: Loss={avg_loss:.4f}")
    
    # 评估分层学习率
    val_loss2, metrics2 = evaluate_model(model2, dev_loader, trainer2, token2idx, idx2token)
    results['layered'] = {
        'val_loss': val_loss2,
        'metrics': metrics2,
        'training_losses': trainer2.training_losses,
        'learning_rates': trainer2.learning_rates
    }
    
    logger.info(f"分层LR结果: F1={metrics2['f1']:.4f}, P={metrics2['precision']:.4f}, R={metrics2['recall']:.4f}")
    
    # 绘制对比图
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    plot_path = output_dir / "lr_strategy_comparison.png"
    
    plot_learning_curves(
        results['onecycle']['training_losses'],
        results['layered']['training_losses'],
        results['onecycle']['learning_rates'],
        results['layered']['learning_rates'],
        plot_path
    )
    
    # 输出对比结果
    logger.info("\n📊 对比实验结果:")
    logger.info("=" * 60)
    logger.info(f"{'策略':<15} {'F1':<8} {'精确率':<8} {'召回率':<8} {'验证损失':<10}")
    logger.info("-" * 60)
    logger.info(f"{'OneCycle':<15} {metrics1['f1']:<8.4f} {metrics1['precision']:<8.4f} {metrics1['recall']:<8.4f} {val_loss1:<10.4f}")
    logger.info(f"{'分层学习率':<15} {metrics2['f1']:<8.4f} {metrics2['precision']:<8.4f} {metrics2['recall']:<8.4f} {val_loss2:<10.4f}")
    logger.info("=" * 60)
    
    # 推荐策略
    if metrics1['f1'] > metrics2['f1']:
        winner = "OneCycle"
        improvement = (metrics1['f1'] - metrics2['f1']) / metrics2['f1'] * 100 if metrics2['f1'] > 0 else 0
    else:
        winner = "分层学习率"
        improvement = (metrics2['f1'] - metrics1['f1']) / metrics1['f1'] * 100 if metrics1['f1'] > 0 else 0
    
    logger.info(f"\n🏆 推荐策略: {winner}")
    logger.info(f"📈 性能提升: {improvement:.2f}%")
    logger.info(f"📊 学习曲线已保存到: {plot_path}")
    
    return results


if __name__ == "__main__":
    run_comparison_experiment()
