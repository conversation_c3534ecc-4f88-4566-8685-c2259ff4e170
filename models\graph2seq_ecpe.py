"""
Graph2Seq Model for Emotion-Cause Pair Extraction

Integrates graph encoder and decoder for extracting emotion-cause pairs from dialogues.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoConfig

from .graph_encoders import GNN, <PERSON>hancedGATEncoder, NUM_EDGE_TYPES
from .graph_decoder import Graph2SeqDecoder
from utils.graphify import batch_graphify, create_utterance_features


class Graph2SeqECPE(nn.Module):
    """
    Graph2Seq model for Emotion-Cause Pair Extraction in dialogues.
    
    The model first encodes dialogues as graphs, then decodes emotion-cause pairs.
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 加载预训练语言模型 - 移除硬编码路径
        # if hasattr(config, 'plm_path') and config.plm_path:
        #     # 使用配置中指定的路径
        #     PLM_path = config.plm_path
        #     local_files_only = True
        # else:
        #     # 使用模型名称，从HuggingFace下载
        #     PLM_path = config.plm_model
        #     local_files_only = False

        # try:
        #     self.plm_config = AutoConfig.from_pretrained(PLM_path, local_files_only=local_files_only)
        #     self.plm = AutoModel.from_pretrained(PLM_path, local_files_only=local_files_only)
        # except Exception as e:
        #     print(f"Warning: Failed to load PLM from {PLM_path}, trying without local_files_only")
        #     self.plm_config = AutoConfig.from_pretrained(config.plm_model, local_files_only=False)
        #     self.plm = AutoModel.from_pretrained(config.plm_model, local_files_only=False)
        # 加载预训练语言模型
        PLM_path="/root/"+config.plm_model
        self.plm_config = AutoConfig.from_pretrained(PLM_path, local_files_only=True)
        self.plm = AutoModel.from_pretrained(PLM_path, local_files_only=True)
        
        # 如果指定，冻结预训练模型参数
        if config.freeze_plm:
            for param in self.plm.parameters():
                param.requires_grad = False
                
        # 话语特征投影层
        self.utt_proj = nn.Linear(self.plm_config.hidden_size, config.hidden_size)
        
        # 使用简化的边类型数量
        self.config.num_edge_types = getattr(config, 'num_edge_types', NUM_EDGE_TYPES)
        
        # 图编码器 - 支持增强功能
        gnn_config = {
            'in_channels': config.hidden_size,
            'hidden_channels': config.hidden_size,
            'out_channels': config.hidden_size,
            'num_layers': config.gnn_layers,
            'dropout': config.dropout,
            'gnn_mode': getattr(config, 'gnn_mode', 'gcn'),
            'num_edge_types': getattr(config, 'num_edge_types', 5),
            'pooling_method': getattr(config, 'pooling_method', 'attention'),
            'use_neighbor_sampling': getattr(config, 'use_neighbor_sampling', False),
            'sample_size': getattr(config, 'neighbor_sample_size', 10),
            'sampling_strategy': getattr(config, 'sampling_strategy', 'uniform'),
            'use_gated_aggregator': getattr(config, 'use_gated_aggregator', False),
            'aggregator_type': 'gated_mean'
        }

        # 选择图编码器类型
        encoder_type = getattr(config, 'graph_encoder_type', 'gnn')  # 'gnn' 或 'gat'

        if encoder_type == 'gat':
            # 使用增强的GAT编码器
            self.graph_encoder = EnhancedGATEncoder(
                input_size=config.hidden_size,
                hidden_size=config.hidden_size,
                num_layers=getattr(config, 'gat_layers', 2),
                num_heads=getattr(config, 'gat_heads', 8),
                num_edge_types=getattr(config, 'num_edge_types', 13),
                dropout=config.dropout,
                use_position_encoding=getattr(config, 'use_position_encoding', True),
                pooling_method=getattr(config, 'pooling_method', 'attention')
            )
        else:
            # 使用原始的GNN编码器
            self.graph_encoder = GNN(**gnn_config)
        
        # 图解码器
        self.graph_decoder = Graph2SeqDecoder(
            hidden_size=config.hidden_size,
            vocab_size=config.vocab_size,
            num_layers=config.decoder_layers,
            dropout=config.dropout,
            max_length=config.max_target_length
        )
        
    def forward(self, batch, target_seq=None, teacher_forcing_ratio=0.5):
        """
        Forward pass of the Graph2Seq model
        
        Args:
            batch: Tuple containing dialogue data
                dialog_tokens: Token IDs [batch_size, seq_len]
                dialog_uttid: Utterance IDs for each token [batch_size, seq_len]
                dialog_mask: Mask for tokens [batch_size, seq_len]
                utt_mask: Mask for utterances [batch_size, max_n_utt]
                utt_speakers: Speaker IDs [batch_size, max_n_utt]
                utt_emotions: Emotion labels [batch_size, max_n_utt]
                edge_index_batch: List of edge indices for each dialogue
                edge_type_batch: List of edge types for each dialogue
                edge_norm_batch: List of edge weights for each dialogue
                emotion_cause_edge_indices_batch: List of indices for emotion-cause edges
            target_seq: Target sequence for teacher forcing [batch_size, seq_len]
            teacher_forcing_ratio: Probability of using teacher forcing
            
        Returns:
            decoder_outputs: Predicted token probabilities
            attention_weights: Attention weights for nodes
        """
        device = next(self.parameters()).device
        
        # 安全检查输入数据
        if not isinstance(batch, (list, tuple)) or len(batch) < 9:
            raise ValueError("Batch must be a tuple/list containing at least 9 elements")
        
        # 处理输入数据
        if len(batch) >= 10:
            # 新格式，包含情感-原因边索引
            dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, \
            utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch, emotion_cause_edge_indices_batch = batch
        else:
            # 旧格式，不包含情感-原因边索引
            dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, \
            utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch = batch
            # 创建空的情感-原因边索引
            emotion_cause_edge_indices_batch = [[] for _ in range(len(edge_index_batch))]
        
        # 将数据移至设备
        dialog_tokens = dialog_tokens.to(device)
        dialog_uttid = dialog_uttid.to(device)
        dialog_mask = dialog_mask.to(device)
        
        # 通过预训练模型获取token嵌入
        with torch.set_grad_enabled(not self.config.freeze_plm):
            plm_outputs = self.plm(
                input_ids=dialog_tokens,
                attention_mask=dialog_mask,
                return_dict=True
            )
            token_embs = plm_outputs.last_hidden_state
        
        # 创建话语级特征
        utterance_features, lengths = create_utterance_features(
            token_embs, dialog_uttid, dialog_mask, self.config.hidden_size, device
        )
        
        # 投影话语特征
        projected_features = self.utt_proj(utterance_features)
        
        # 转换对话为图结构
        node_features, edge_index, edge_norm, edge_type, batch_indices = batch_graphify(
            projected_features, lengths, utt_speakers, 
            edge_index_batch, edge_type_batch, edge_norm_batch, device
        )
        
        # 编码图
        node_embeddings, graph_embedding = self.graph_encoder(
            node_features, edge_index, edge_type, edge_norm, batch_indices
        )
        
        # 准备情感-原因节点信息
        emotion_cause_nodes = []
        if emotion_cause_edge_indices_batch:
            # 创建情感-原因节点索引映射
            node_offset = 0
            batch_to_nodes = {}
            
            # 为每个batch收集节点起始位置
            for b in range(batch_indices.max().item() + 1):
                batch_nodes = (batch_indices == b).nonzero(as_tuple=True)[0]
                if batch_nodes.size(0) > 0:
                    batch_to_nodes[b] = (batch_nodes.min().item(), batch_nodes.max().item())
                else:
                    batch_to_nodes[b] = (node_offset, node_offset)
                
            # 为每个batch收集情感-原因节点
            for b in range(len(emotion_cause_edge_indices_batch)):
                ec_nodes = set()
                if b < len(edge_index_batch) and b < len(emotion_cause_edge_indices_batch):
                    # 获取当前batch的边和情感-原因边索引
                    edges = edge_index_batch[b]
                    ec_indices = emotion_cause_edge_indices_batch[b]
                    
                    # 当前batch的节点偏移
                    if b in batch_to_nodes:
                        start_idx, _ = batch_to_nodes[b]
                        
                        # 收集边的节点
                        for idx in ec_indices:
                            if 0 <= idx < len(edges):
                                src, tgt = edges[idx]
                                # 添加全局偏移
                                ec_nodes.add(src + start_idx)
                
                emotion_cause_nodes.append(list(ec_nodes))
        
        # 如果情感-原因节点列表为空，填充空列表
        while len(emotion_cause_nodes) < dialog_tokens.size(0):
            emotion_cause_nodes.append([])
        
        # 解码生成情感-原因对
        decoder_outputs, attention_weights = self.graph_decoder(
            node_embeddings, graph_embedding, target_seq, batch_indices, 
            teacher_forcing_ratio, emotion_cause_nodes
        )
        
        return decoder_outputs, attention_weights
    
    def predict(self, batch, max_length=20):
        """
        Make predictions using improved greedy decoding

        Args:
            batch: Input batch
            max_length: Maximum sequence length

        Returns:
            predictions: Generated sequences [batch_size, max_length]
        """
        self.eval()
        device = next(self.parameters()).device

        with torch.no_grad():
            # 使用标准的前向传播进行预测
            # 创建虚拟目标序列（只包含SOS）
            sos_token_id = 1  # <sos>
            eos_token_id = 2  # <eos>
            pad_token_id = 0  # <pad>

            # 确定批次大小
            if len(batch) >= 10:
                utt_mask = batch[3]
            else:
                utt_mask = batch[3]
            batch_size = utt_mask.size(0)

            # 创建预测序列
            predictions = torch.full((batch_size, max_length), pad_token_id, dtype=torch.long, device=device)
            predictions[:, 0] = sos_token_id

            # 逐步生成
            for step in range(1, max_length):
                # 使用当前序列作为目标序列进行前向传播
                current_seq = predictions[:, :step+1].clone()
                current_seq[:, step] = pad_token_id  # 当前位置设为pad，让模型预测

                # 前向传播
                decoder_outputs, _ = self.forward(batch, current_seq, teacher_forcing_ratio=0.0)

                # 获取当前步的logits
                current_logits = decoder_outputs[:, step-1, :]  # [batch_size, vocab_size]

                # 应用温度缩放和概率调整
                temperature = 0.8
                current_logits = current_logits / temperature

                # 对某些token进行惩罚以避免重复
                # 惩罚连续的相同token
                if step > 1:
                    prev_token = predictions[:, step-1]
                    for b in range(batch_size):
                        current_logits[b, prev_token[b]] -= 1.0  # 降低重复概率

                # 贪婪解码
                next_tokens = torch.argmax(current_logits, dim=-1)
                predictions[:, step] = next_tokens

                # 检查是否遇到EOS
                finished_mask = (next_tokens == eos_token_id)
                if torch.all(finished_mask):
                    break

            return predictions

    def _encode_graph_internal(self, batch):
        """内部图编码方法"""
        device = next(self.parameters()).device

        # 安全检查输入数据
        if not isinstance(batch, (list, tuple)) or len(batch) < 9:
            raise ValueError("Batch must be a tuple/list containing at least 9 elements")

        # 解包批次数据
        if len(batch) >= 10:
            dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, \
            utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch, _ = batch
        else:
            dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, \
            utt_emotions, edge_index_batch, edge_type_batch, edge_norm_batch = batch

        # 移动到设备
        dialog_tokens = dialog_tokens.to(device)
        dialog_mask = dialog_mask.to(device)
        utt_mask = utt_mask.to(device)
        utt_speakers = utt_speakers.to(device)
        utt_emotions = utt_emotions.to(device)

        # 获取PLM嵌入
        plm_outputs = self.plm(input_ids=dialog_tokens, attention_mask=dialog_mask)
        token_embeddings = plm_outputs.last_hidden_state

        # 创建话语特征
        batch_size = dialog_tokens.size(0)

        utterance_features = []
        batch_indices = []

        for b in range(batch_size):
            valid_utts = utt_mask[b].sum().item()

            for u in range(valid_utts):
                # 获取话语的token掩码
                utt_token_mask = (dialog_uttid[b] == u + 1)

                if utt_token_mask.any():
                    # 平均池化话语tokens
                    utt_tokens = token_embeddings[b][utt_token_mask]
                    utt_feature = utt_tokens.mean(dim=0)
                else:
                    # 如果没有找到对应tokens，使用零向量
                    utt_feature = torch.zeros(token_embeddings.size(-1), device=device)

                # 投影到隐藏维度
                utt_feature = self.utt_proj(utt_feature)
                utterance_features.append(utt_feature)
                batch_indices.append(b)

        if not utterance_features:
            # 如果没有话语，创建虚拟特征
            utterance_features = [torch.zeros(self.config.hidden_size, device=device)]
            batch_indices = [0]

        # 堆叠特征
        node_features = torch.stack(utterance_features)
        batch_indices = torch.tensor(batch_indices, device=device)

        # 构建图结构
        from utils.graphify import batch_graphify

        # 创建话语特征和长度信息
        utterance_features_tensor = node_features.unsqueeze(0)  # [1, num_nodes, hidden_size]
        lengths_tensor = torch.tensor([node_features.size(0)], device=device)  # [1]
        speakers_tensor = torch.zeros(1, node_features.size(0), device=device)  # 虚拟说话者

        # 调用batch_graphify
        _, edge_index, edge_norm, edge_type, _ = batch_graphify(
            utterance_features_tensor, lengths_tensor, speakers_tensor,
            edge_index_batch, edge_type_batch, edge_norm_batch, device
        )

        # 图编码
        if edge_index.size(1) > 0:
            node_embeddings, graph_embedding = self.graph_encoder(
                node_features, edge_index, edge_type, edge_norm, batch_indices
            )
        else:
            # 没有边的情况
            node_embeddings = node_features
            graph_embedding = torch.zeros(batch_size, self.config.hidden_size, device=device)

        return node_embeddings, graph_embedding


def create_ecpe_vocab(emotion_categories, max_utterances=100):
    """
    Create vocabulary for emotion-cause pairs using utterance ID format.

    The vocabulary includes:
    - Special tokens: <pad>, <sos>, <eos>, <sep>
    - Utterance ID tokens: utt_000, utt_001, ..., utt_099
    - Emotion tokens

    Format of output sequence:
    <sos> utt_001 emotion1 utt_002 <sep> utt_003 emotion3 utt_004 <sep> ... <eos>

    Args:
        emotion_categories: List of emotion categories
        max_utterances: Maximum number of utterances (default: 100)

    Returns:
        token2idx: Dictionary mapping tokens to indices
        idx2token: Dictionary mapping indices to tokens
    """
    # 特殊标记在词汇表中的位置固定
    tokens = ['<pad>', '<sos>', '<eos>', '<sep>']

    # 添加话语ID和情感类别，避免重复
    all_tokens = set(tokens)

    # 添加话语ID tokens
    if max_utterances is None:
        max_utterances = 100  # 默认最大话语数

    for i in range(max_utterances):
        utt_id = f"utt_{i:03d}"  # utt_000, utt_001, ..., utt_099
        if utt_id not in all_tokens:
            tokens.append(utt_id)
            all_tokens.add(utt_id)

    # 添加情感类别
    for emotion in emotion_categories:
        if emotion not in all_tokens:
            tokens.append(emotion)
            all_tokens.add(emotion)

    # 创建映射
    token2idx = {token: idx for idx, token in enumerate(tokens)}
    idx2token = {idx: token for token, idx in token2idx.items()}

    return token2idx, idx2token