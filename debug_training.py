#!/usr/bin/env python3
"""
调试训练脚本
诊断损失值异常大的问题
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging

from config import get_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences
from parser import parse_args_and_create_config, print_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DebugLoss(nn.Module):
    """调试损失函数"""
    
    def __init__(self, token2idx):
        super().__init__()
        self.pad_idx = token2idx.get('<pad>', 0)
        self.criterion = nn.CrossEntropyLoss(ignore_index=self.pad_idx, reduction='none')
    
    def forward(self, logits, targets):
        batch_size, seq_len, vocab_size = logits.shape
        
        # 检查输入
        logger.info(f"🔍 调试信息:")
        logger.info(f"  logits形状: {logits.shape}")
        logger.info(f"  targets形状: {targets.shape}")
        logger.info(f"  logits范围: [{logits.min().item():.4f}, {logits.max().item():.4f}]")
        logger.info(f"  targets范围: [{targets.min().item()}, {targets.max().item()}]")
        logger.info(f"  logits是否包含NaN: {torch.isnan(logits).any().item()}")
        logger.info(f"  logits是否包含Inf: {torch.isinf(logits).any().item()}")
        
        # 计算损失
        logits_flat = logits.view(-1, vocab_size)
        targets_flat = targets.view(-1)
        
        # 创建掩码
        mask = targets_flat != self.pad_idx
        valid_count = mask.sum().item()
        logger.info(f"  有效token数量: {valid_count}/{targets_flat.numel()}")
        
        if valid_count == 0:
            logger.warning("  ⚠️ 没有有效的token！")
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 计算每个位置的损失
        losses = self.criterion(logits_flat, targets_flat)
        valid_losses = losses[mask]
        
        logger.info(f"  损失范围: [{valid_losses.min().item():.4f}, {valid_losses.max().item():.4f}]")
        logger.info(f"  平均损失: {valid_losses.mean().item():.4f}")
        
        # 检查异常损失
        high_loss_mask = valid_losses > 20.0
        if high_loss_mask.any():
            logger.warning(f"  ⚠️ 发现{high_loss_mask.sum().item()}个异常高损失值")
            
        return valid_losses.mean()


def debug_single_batch():
    """调试单个批次"""
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)
    
    config.training.batch_size = 2  # 使用小批次
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    
    logger.info("🔧 开始调试单个批次...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    data_path = config.dataset.get_data_path()
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,  # 不打乱，便于调试
        collate_fn=multimodal_collate_fn,
        num_workers=0  # 不使用多进程
    )
    
    # 创建词汇表
    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)
    
    logger.info(f"词汇表大小: {config.model.vocab_size}")
    
    # 创建模型
    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    
    # 调试损失函数
    debug_criterion = DebugLoss(token2idx)
    
    # 获取第一个批次
    batch = next(iter(train_loader))
    
    logger.info("📊 批次信息:")
    for i, item in enumerate(batch):
        if isinstance(item, torch.Tensor):
            logger.info(f"  batch[{i}]: {item.shape} {item.dtype}")
        elif isinstance(item, list):
            logger.info(f"  batch[{i}]: list长度={len(item)}")
        else:
            logger.info(f"  batch[{i}]: {type(item)}")
    
    # 准备目标序列
    logger.info("\n🎯 准备目标序列...")
    target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
    target_seqs = target_seqs.to(device)
    
    logger.info(f"目标序列形状: {target_seqs.shape}")
    logger.info(f"目标序列样例: {target_seqs[0][:10]}")
    
    # 转换为token查看
    target_tokens = [idx2token.get(idx.item(), f'<{idx.item()}>') for idx in target_seqs[0][:10]]
    logger.info(f"目标tokens: {target_tokens}")
    
    # 模型前向传播
    logger.info("\n🚀 模型前向传播...")
    model.train()
    
    try:
        decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.9)
        logger.info(f"解码器输出形状: {decoder_outputs.shape}")
        
        # 计算损失
        logger.info("\n💥 计算损失...")
        loss = debug_criterion(decoder_outputs, target_seqs)
        
        logger.info(f"最终损失: {loss.item():.6f}")
        
        # 检查梯度
        logger.info("\n🔄 检查梯度...")
        loss.backward()
        
        total_norm = 0
        param_count = 0
        for name, param in model.named_parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
                
                if param_norm > 10.0:  # 检查梯度爆炸
                    logger.warning(f"  ⚠️ 大梯度: {name} = {param_norm:.4f}")
        
        total_norm = total_norm ** (1. / 2)
        logger.info(f"总梯度范数: {total_norm:.4f}")
        logger.info(f"参数数量: {param_count}")
        
        if total_norm > 100.0:
            logger.error("🚨 梯度爆炸！")
        elif total_norm < 1e-6:
            logger.warning("⚠️ 梯度消失！")
        else:
            logger.info("✅ 梯度正常")
            
    except Exception as e:
        logger.error(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()


def test_different_learning_rates():
    """测试不同学习率的效果"""
    logger.info("\n🧪 测试不同学习率...")
    
    learning_rates = [1e-6, 5e-6, 1e-5, 5e-5, 1e-4]
    
    for lr in learning_rates:
        logger.info(f"\n📊 测试学习率: {lr}")
        
        args = parse_args_and_create_config()
        config = get_config()
        config.update_from_args(args)
        
        config.training.batch_size = 2
        config.model.use_multimodal = True
        config.model.modalities = ['text', 'visual', 'audio']
        config.model.speakers = config.dataset.get_speakers()
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建数据
        data_path = config.dataset.get_data_path()
        train_file = os.path.join(data_path, "meld_train_multimodal.h5")
        
        train_dataset = MultimodalDialogDataset(
            h5_file_path=train_file,
            modalities=['text', 'visual', 'audio']
        )
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=config.training.batch_size,
            shuffle=False,
            collate_fn=multimodal_collate_fn,
            num_workers=0
        )
        
        # 创建词汇表和模型
        emotion_categories = config.dataset.get_emotion_categories()
        token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
        config.model.vocab_size = len(token2idx)
        
        model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
        optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
        criterion = nn.CrossEntropyLoss(ignore_index=token2idx.get('<pad>', 0))
        
        # 训练几步
        model.train()
        losses = []
        
        for step, batch in enumerate(train_loader):
            if step >= 5:  # 只训练5步
                break
                
            try:
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                target_seqs = target_seqs.to(device)
                
                if target_seqs.sum() == 0:
                    continue
                
                optimizer.zero_grad()
                decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.9)
                
                loss = criterion(
                    decoder_outputs.view(-1, decoder_outputs.size(-1)),
                    target_seqs.view(-1)
                )
                
                if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 1e6:
                    logger.warning(f"  步骤{step}: 异常损失 {loss.item():.2e}")
                    break
                
                loss.backward()
                
                # 检查梯度
                total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), float('inf'))
                
                if total_norm > 100:
                    logger.warning(f"  步骤{step}: 梯度爆炸 {total_norm:.2f}")
                    break
                
                optimizer.step()
                losses.append(loss.item())
                
                logger.info(f"  步骤{step}: 损失={loss.item():.4f}, 梯度范数={total_norm:.4f}")
                
            except Exception as e:
                logger.error(f"  步骤{step}: 错误 {e}")
                break
        
        if losses:
            avg_loss = np.mean(losses)
            logger.info(f"学习率{lr}: 平均损失={avg_loss:.4f}, 稳定性={'✅' if avg_loss < 20 else '❌'}")
        else:
            logger.info(f"学习率{lr}: 训练失败 ❌")


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


if __name__ == "__main__":
    set_seed(42)
    
    logger.info("🔍 开始调试训练问题...")
    
    # 调试单个批次
    debug_single_batch()
    
    # 测试不同学习率
    test_different_learning_rates()
    
    logger.info("\n✅ 调试完成！")
