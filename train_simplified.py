#!/usr/bin/env python3
"""
简化的多模态Graph2ECPE训练脚本
专注于稳定性和核心功能
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
import os
from pathlib import Path

from data import MultimodalDialogDataset, multimodal_collate_fn
from models.multimodal_graph2seq_ecpe_fixed import MultimodalGraph2SeqECPEFixed
from models import create_ecpe_vocab
from utils import prepare_target_sequences, compute_ecpe_metrics, UnifiedPostProcessor
from parser_simplified import create_config_from_args, print_simplified_args

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimplifiedECPELoss(nn.Module):
    """简化的ECPE损失函数"""
    
    def __init__(self, token2idx, label_smoothing=0.1):
        super().__init__()
        self.pad_idx = token2idx.get('<pad>', 0)
        self.criterion = nn.CrossEntropyLoss(
            ignore_index=self.pad_idx,
            label_smoothing=label_smoothing
        )
    
    def forward(self, logits, targets):
        """计算损失"""
        batch_size, seq_len, vocab_size = logits.shape
        return self.criterion(
            logits.view(-1, vocab_size), 
            targets.view(-1)
        )


class StableTrainer:
    """稳定的训练器"""
    
    def __init__(self, model, token2idx, config):
        self.model = model
        self.token2idx = token2idx
        self.config = config
        
        # 损失函数
        self.criterion = SimplifiedECPELoss(token2idx)
        
        # 优化器
        self.optimizer = torch.optim.AdamW([
            {'params': model.plm.parameters(), 'lr': config.training.learning_rate * 0.1},
            {'params': [p for n, p in model.named_parameters() if 'plm' not in n], 
             'lr': config.training.learning_rate}
        ], weight_decay=config.training.weight_decay)
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=5, T_mult=2, eta_min=config.training.learning_rate * 0.01
        )
        
        # 训练状态
        self.best_f1 = 0.0
        self.patience_counter = 0
        
    def get_teacher_forcing_ratio(self, epoch):
        """获取teacher forcing比率"""
        initial_ratio = 0.9
        final_ratio = 0.3
        decay_rate = 0.95
        ratio = final_ratio + (initial_ratio - final_ratio) * (decay_rate ** epoch)
        return max(ratio, final_ratio)
    
    def train_step(self, batch, epoch):
        """执行一个训练步骤"""
        try:
            device = next(self.model.parameters()).device
            target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
            target_seqs = target_seqs.to(device)
            
            if target_seqs.sum() == 0:
                return None
            
            teacher_forcing_ratio = self.get_teacher_forcing_ratio(epoch)
            
            self.optimizer.zero_grad()
            decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            
            loss = self.criterion(decoder_outputs, target_seqs)
            
            if torch.isnan(loss) or torch.isinf(loss):
                return None
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            self.scheduler.step()
            
            return loss.item()
            
        except Exception as e:
            logger.warning(f"训练步骤出错: {e}")
            return None
    
    def validate(self, val_loader, idx2token):
        """验证模型"""
        self.model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="验证"):
                try:
                    device = next(self.model.parameters()).device
                    target_seqs = prepare_target_sequences(batch, self.token2idx, max_length=25)
                    target_seqs = target_seqs.to(device)
                    
                    decoder_outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = self.criterion(decoder_outputs, target_seqs)
                    val_loss += loss.item()
                    val_batches += 1
                    
                    # 贪婪解码
                    predictions = torch.argmax(decoder_outputs, dim=-1)
                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)
                    
                except Exception as e:
                    logger.warning(f"验证批次出错: {e}")
                    continue
        
        avg_val_loss = val_loss / max(1, val_batches)
        
        # 计算指标
        if all_predictions and all_targets:
            combined_predictions = torch.cat(all_predictions, dim=0)
            combined_targets = torch.cat(all_targets, dim=0)
            
            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
        
        return avg_val_loss, metrics
    
    def should_early_stop(self, current_f1, patience=5):
        """判断是否早停"""
        if current_f1 > self.best_f1:
            self.best_f1 = current_f1
            self.patience_counter = 0
            return False, True
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience, False


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def create_datasets(config):
    """创建数据集"""
    data_path = config.dataset.get_data_path()
    
    train_file = os.path.join(data_path, "meld_train_multimodal.h5")
    dev_file = os.path.join(data_path, "meld_dev_multimodal.h5")
    
    for file_path in [train_file, dev_file]:
        if not os.path.exists(file_path):
            logger.error(f"数据文件不存在: {file_path}")
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_file,
        modalities=['text', 'visual', 'audio']
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_file,
        modalities=['text', 'visual', 'audio']
    )
    
    return train_dataset, dev_dataset


def main():
    """主训练函数"""
    config, args = create_config_from_args()

    # 设置多模态配置
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()

    config.print_config()
    print_simplified_args(args)
    
    logger.info("🎯 开始简化稳定训练...")
    
    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset, dev_dataset = create_datasets(config)
    
    # 创建词汇表
    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)
    
    logger.info(f"词汇表大小: {config.model.vocab_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )
    
    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )
    
    # 创建模型
    model = MultimodalGraph2SeqECPEFixed(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 创建训练器
    trainer = StableTrainer(model, token2idx, config)
    
    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    target_f1 = 0.6
    
    logger.info(f"🎯 训练配置:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  简化损失函数: 启用")
    logger.info(f"  稳定训练策略: 启用")
    
    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc="训练"):
            loss = trainer.train_step(batch, epoch)
            if loss is not None:
                train_loss += loss
                train_batches += 1
        
        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")
        
        # 验证阶段
        avg_val_loss, metrics = trainer.validate(dev_loader, idx2token)
        
        logger.info(f"验证损失: {avg_val_loss:.4f}")
        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
        
        # 早停检查
        should_stop, is_best = trainer.should_early_stop(metrics['f1'], patience=5)
        
        if is_best:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")
            
            # 保存模型
            output_dir = Path(config.experiment.output_dir)
            output_dir.mkdir(exist_ok=True)
            model_save_path = output_dir / f"best_simplified_model_{config.dataset.name}.pt"
            torch.save(model.state_dict(), model_save_path)
            logger.info(f"  模型已保存到: {model_save_path}")
            
            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")
        
        if should_stop:
            logger.info(f"早停触发: 连续5次未改善")
            break
    
    logger.info(f"\n🎯 训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    
    return best_f1, best_metrics['precision'], best_metrics['recall']


if __name__ == "__main__":
    main()
